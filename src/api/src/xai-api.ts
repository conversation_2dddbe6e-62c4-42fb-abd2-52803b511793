import XRequest from './base-request'
import type { IDifyApiOptions, IGetConversationHistoryResponse } from './dify-api'


type BaseResponse = {}


// 根据下方创建interface
export interface IGetAiAppInfoResponse {
	id: number
	appUuid: string
	appLang: string
	appStatus: string
	appName: string
	appNameEn: string
	appType: string
	appDescription: string
	appIcon: string
	appUser: AppUser
	feeTypes: FeeType[]
	isInternalUser: number
	dAppUuid: string
	directoryMd: string
}
export interface AppUser {
	id: number;
	socialUserId: number;
	socialType: number;
	appUuid: string;
	status: string;
	useNum: string;
	expireAt: string; // 日期格式字符串，如 "2025-07-01"
  }
export interface FeeType {
	online: number;
	packageKey: string;
	type: string;
	periodType: string;
	monthNum: number;
	oldPrice: number;
	feePrice: number;
	coinType: string;
	priceId: string;
	num: number;
  }

 export interface PackageByKey {
	id: number
	socialUserId: number
	socialType: number
	packageKey: string
	packageType: string
	subStatus: number
	subAt: string
	unSubAt: string
	startAt: string
	expireAt: string
	feeTypes: FeeType[]
  }
  
  export interface GetSubOrderResponse {
    appOrderId: string
	payAmount: string
	payStatus: string
    createdTime: string
    itemOrigAmt: number
    itemId: number
    mobile: string
  }
  
  export interface PurchaseRecord {
    userName: string
    appName: string
  }

  // 意见反馈接口相关类型定义
  export interface FeedbackAccessory {
    url: string
  }

  export interface FeedbackData {
    projectId: number
    mgTitle: string
    mgRealName: string
    mgTell: string
    mgEmail: string
    mgUnit: string
    mgContent: string
    mgAccessoryList: FeedbackAccessory[]
    mgIsPublic: number
    mgType: number
    mgSource: string
    mgUserid: number
    mgUsername: string
    versionInfo: string
    moduleType: string
    objectId: number
    objectTitle: string
    clientIp: string
  }

  export interface FeedbackResponse {
    status: number
    message: string
    data: any
  }

  // 图片上传接口相关类型定义
  export interface ImageUploadResponse {
    status: number
    message: string
    data: {
      url: string
      fileKey?: string
      size?: number
      width?: number
      height?: number
    }
  }

  // 登录相关接口响应类型
export interface LoginResponse {
	token: string
	htoken?: string
	openid?: string
	socialUserId?: string
	socialType?: string
	userInfo?: any
}

export interface knowledgeItem {

	id: number
	socialUserId: number
	socialType: number
	pid: number
	fileName: string
	cosFilePath: string
	cdnSignedUrl: string
	fileSize: number
	fileMd5: string
	t: number
	difyInfo: string
	type: number
	level: number
	createdAt: string

}


/**
 * 应用列表 CRUD 的 RESTful 实现
 */
export class XAiApi {

	constructor(options: IDifyApiOptions) {
		this.options = options
		this.baseRequest = new XRequest({
			baseURL: options.apiBase,
			medxyToken: options.medxyToken,
			appId: '',
		})
	}

	options: IDifyApiOptions
	baseRequest: XRequest

	updateOptions(options: IDifyApiOptions) {
		this.options = options
		this.baseRequest = new XRequest({
			baseURL: options.apiBase,
			medxyToken: options.medxyToken,
			appId: '',
		})
	}



    async getAppByUuid(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.get('/ai-base/index/getAppByUuid',params,headers) as Promise<IGetAiAppInfoResponse>;
	}
	async createSubscription(params: Record<string, unknown> = {}, headers:Record<string, string> = {} ): Promise<string> {
        return  this.baseRequest.post('/ai-base/appUser/createSubscription',params,headers) as Promise<string>;
	}
    async getAiWriteToken(params: Record<string, unknown> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.post('/ai-base/index/getAiWriteToken',params,headers) as Promise<BaseResponse>;
	}
    async getSubOrder(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.get('/ai-base/index/getSubOrder',params,headers) as Promise<GetSubOrderResponse>;
	}
    async createAliSub(params: Record<string, string> = {}, headers:Record<string, string> = {} ): Promise<string> {
        return  this.baseRequest.get('/ai-base/index/createAliSub',params,headers) as Promise<string>;
	}
    async freeLimit(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.get('/ai-base/index/free-limit',params,headers) as Promise<BaseResponse>;
	}
    async getPackageByKey(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.get('/ai-base/index/getPackageByKey',params,headers) as Promise<PackageByKey>;
	}
    async getPackageByDomain(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.get('/ai-base/index/getPackageByDomain',params,headers) as Promise<Record<string, PackageByKey>>;
	}
    async cancelSubscription(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.post('/ai-base/appUser/cancelSubscription?appUuid=',{}, headers) as Promise<BaseResponse>;
	}
    // 查询所有的应用
    async getAppList(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.post('/ai-base/index/getAppList',params,headers) as Promise<BaseResponse>;
	}
    // 查询所有的应用
    async getAppByConfigKey(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.post('/ai-base/index/getAppByConfigKey',params,headers) as Promise<IGetAiAppInfoResponse[]>;
	}
    // 查询所有的应用
    async getAppByDomain(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.post('/ai-base/index/getAppByDomain',params,headers) as Promise<IGetAiAppInfoResponse[]>;
	}
	// 查询活动pro的订阅次数
	async bindAppUser(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
		return  this.baseRequest.post(`/ai-base/appUser/bindAppUser?appUuid=${params.appUuid}&appNameEn=${params.appNameEn}`,{},headers) as Promise<BaseResponse>;
	}
	// 查询活动pro的订阅次数
	async qaList(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
		return  this.baseRequest.post(`/ai-base/index/qa-list`,params,headers) as Promise<BaseResponse>;
	}
	// 获取配置项
	async getConfigByKeyFromCache(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.get('/ai-base/index/getConfigByKeyFromCache',params,headers);
	}
	// 上传到参赛作品
	async uploadToCompetition(file: File) {
		const formData = new FormData()
		formData.append('file', file)
		const response = await this.baseRequest
			.baseRequest('/ai-base/knowledge/uploadToCompetition', {
				method: 'POST',
				body: formData,
			})
		const result = await response.json()
		if (result.code === 0) {
			return result.data
		} else {
			throw new Error(result.msg || '上传失败')
		}
	}
	// 订阅信息
	async getPurchaseRecords(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.get('/ai-base/index/getPurchaseRecords',params,headers) as Promise<PurchaseRecord[]>;
	}
	// 清除cookie中用户信息
	async logout() {
        return  this.baseRequest.cleanCookies();
	}
	async getAppByNameEn(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        return  this.baseRequest.get('/ai-base/index/getAppByNameEn',params,headers) as Promise<IGetAiAppInfoResponse>;
	}

	// 提交意见反馈
	async submitFeedback(feedbackData: FeedbackData): Promise<FeedbackResponse> {
		const response = await this.baseRequest.baseRequest('/ai-base/openapi/leave-message', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(feedbackData)
		})
		return await response.json() as FeedbackResponse
	}

	// 上传图片
	async uploadImage(file: File): Promise<FeedbackAccessory> {
		const formData = new FormData()
		formData.append('file', file)
		
		const response = await this.baseRequest.baseRequest('/ai-base/openapi/upload-picture', {
			method: 'POST',
			body: formData
		})
		
		const result = await response.json() as ImageUploadResponse
		
		if (result.status === 200) {
			const url = result.data?.url || ''
			return { url }
		} else {
			throw new Error(result.message || '上传失败')
		}
	}

	// 获取授权登录地址
	async socialAuthRedirect(socialType: number) {
		return this.baseRequest.get(`/ai-base/index/socialAuthRedirect`, { socialType: socialType.toString() }) as Promise<{ url: string }>;
	}

	// 社交登录
	async socialLogin(socialType: string, code: string, state: string) {
		return this.baseRequest.get(`/ai-base/index/socialLogin`, { socialType, code, state }) as Promise<LoginResponse>;
	}

	// 登录
	async login(data: { fromPlatform: string, email: string, password: string }) {
		return this.baseRequest.post(`/ai-base/index/login`, data) as Promise<LoginResponse>;
	}

	// 注册
	async register(data: { userName: string, email: string, password: string, emailCode: string, socialType: number }) {
		return this.baseRequest.post(`/ai-base/index/register`, data) as Promise<LoginResponse>;
	}

	// 发送验证码
	async sendEmailCode(data: { email: string, type: string, socialType?:number }) {
		return this.baseRequest.post(`/ai-base/index/sendEmailCode?email=${data.email}&type=${data.type}&socialType=${data.socialType}`, {}) as Promise<{ success: boolean }>;
	}

	// 退出登录
	async aiLogout() {
		return this.baseRequest.post(`/ai-base/index/logout`, {}) as Promise<BaseResponse>;
	}

	// 知识库列表
	async getKnowledgeByDir(data:{pid: number, fileName: string}) {
		return this.baseRequest.post(`/ai-base/knowledge/getKnowledgeByDir`, data) as Promise<knowledgeItem[]>;
	}

	// 创建知识库
	async createKnowledge(type: number, pid: number, fileName: string, file?: File): Promise<knowledgeItem> {
		const formData = new FormData()
		formData.append('type', type.toString())
		formData.append('pid', pid.toString())
		formData.append('fileName', fileName)
		if (file) {
			formData.append('file', file)
		}
		const response = await this.baseRequest
			.baseRequest('/ai-base/knowledge/create', {
				method: 'POST',
				body: formData,
			})
		const result = await response.json()
		if (result.code === 0) {
			return result.data
		} else {
			throw new Error(result.msg || '上传失败')
		}
	}

	// 修改文件名
	async updateFileName(data:{id: number, fileName: string}) {
		return this.baseRequest.post(`/ai-base/knowledge/updateFileName`, data) as Promise<BaseResponse>;
	}

	// 获取文件路径
	async getFilePath(data: {id: number}) {
		return this.baseRequest.post(`/ai-base/knowledge/getFilePath`, data) as Promise<knowledgeItem[]>;
	}

	// 删除文件
	async deleteKnowledge(data: {id: number}) {
		return this.baseRequest.post(`/ai-base/knowledge/delete?id=${data.id}`, {}) as Promise<BaseResponse>;
	}

	// 根据dify附件id获取签名URL
	async getSignedFileUrl(data: {uploadFileId: string}) {
		return this.baseRequest.get(`/ai-base/index/getSignedFileUrl`, data) as Promise<{ url: string }>;

	}

	// 根据dify会话id获取历史消息，主要用于分享/案例
	async getMessages(data: {encryptionId: string}) {
		return this.baseRequest.get(`/ai-base/index/getMessages`, data) as Promise<IGetConversationHistoryResponse>;

	}


}
