import type React from 'react';
import { formatSize, getFileIcon } from '../../api/src/utils/file-type';
import type { IMessageFileItem } from '../../api/src/types';
import type { IFileType } from '../../api/src/types/file';

interface MessageAttachmentsProps {
  attachments?: IMessageFileItem[];
}

const MessageAttachments: React.FC<MessageAttachmentsProps> = ({ attachments = [] }) => {
  if (!attachments || attachments.length === 0) {
    return null;
  }

  const openAttachment = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div className="mt-3 border-t border-gray-100 pt-3">
      <div className="space-y-2">
        {attachments.map((attachment, index) => (
          <div
            key={index}
            className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-all duration-200"
          >
            <div className="text-xl flex-shrink-0 mr-3">
              {getFileIcon(attachment.type as IFileType)}
            </div>
            <div className="flex-1 min-w-0">
              <span className="block text-sm font-medium text-gray-900 truncate" title={attachment.filename}>
                {attachment.filename}
              </span>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500">{formatSize(attachment.size)}</span>
              </div>
            </div>
            <div className="flex-shrink-0 ml-3 cursor-pointer" onClick={() => openAttachment(attachment.url)}>
              <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor" className="remixicon h-3.5 w-3.5 text-text-tertiary">
                <path d="M3 19H21V21H3V19ZM13 13.1716L19.0711 7.1005L20.4853 8.51472L12 17L3.51472 8.51472L4.92893 7.1005L11 13.1716V2H13V13.1716Z"></path>
              </svg>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MessageAttachments;
