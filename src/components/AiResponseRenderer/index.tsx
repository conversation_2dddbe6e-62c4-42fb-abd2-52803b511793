// AiResponseRenderer.tsx
import React, {
  type FC,
  type ReactNode,
  useMemo,
  useCallback
} from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import 'katex/dist/katex.min.css';
import { BlockMath, InlineMath } from 'react-katex';
import type { Components } from 'react-markdown';
import SimpleMermaid from '../SimpleMermaid';
import { useStyleInjection } from '../../hooks/useStyleInjection';

// Emoji 处理工具函数
const processEmojis = (text: string): string => {
  // 只处理基础的文本表情符号，不处理中文词汇
  const emojiMap: { [key: string]: string } = {
    // 基础表情符号
    '☺': '☺️',
    ':)': '😊',
    ':-)': '😊',
    ':(': '😢',
    ':-(': '😢',
    ':D': '😃',
    ':-D': '😃',
    ';)': '😉',
    ';-)': '😉',
    ':P': '😛',
    ':-P': '😛',
    ':p': '😛',
    ':-p': '😛',
    ':o': '😮',
    ':-o': '😮',
    ':O': '😱',
    ':-O': '😱',
    ':|': '😐',
    ':-|': '😐',
    ':*': '😘',
    ':-*': '😘',
    '<3': '❤️',
    '</3': '💔',

    // 只处理特定的符号转换，不处理中文词汇
    '~': '～',
    '。。。': '…',
    '...': '…'
  };

  let processedText = text;

  // 只处理表情符号，使用精确匹配
  Object.entries(emojiMap).forEach(([textEmoji, emoji]) => {
    // 对于特殊字符，需要转义
    const escapedText = textEmoji.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // 对于表情符号，使用更简单和安全的匹配方式
    if (textEmoji.match(/^[:\-\(\)\[\]<>3pPdDoO\|*]+$/)) {
      // 表情符号需要边界匹配，避免在单词中间替换
      // 使用简单的边界检查，避免复杂的正则表达式
      const parts = processedText.split(textEmoji);
      if (parts.length > 1) {
        processedText = parts.join(emoji);
      }
    } else if (textEmoji === '~') {
      // 只在句末或独立使用时替换波浪号
      processedText = processedText.replace(/~(?=\s|$)/g, emoji);
    } else if (textEmoji === '。。。' || textEmoji === '...') {
      // 省略号直接替换
      const regex = new RegExp(escapedText, 'g');
      processedText = processedText.replace(regex, emoji);
    }
  });

  return processedText;
};



// 错误边界组件
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, { hasError: boolean }> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, info: React.ErrorInfo) {
    console.error("Component render error:", error, info);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || <span className="render-error inline-block">Component render failed</span>;
    }
    return this.props.children;
  }
}

// 主渲染器组件
interface AiResponseRendererProps {
  content: string;
  fontSize?: 'sm' | 'base' | 'lg'; // 新增：字体大小选项
}

const AiResponseRenderer: FC<AiResponseRendererProps> = ({ content, fontSize = 'base' }) => {
  // 使用样式注入Hook来处理HTML中的样式
  const { processContent } = useStyleInjection({
    scopePrefix: '.ai-response-container',
    autoCleanup: true
  });

  // 清理内容中的多余空白和换行 - 保留markdown格式的基础清理
  const cleanContent = (text: string): string => {
    // 首先进行基础清理
    let cleaned = text.trim()

    // 转义可能被误认为HTML标签的小于号（如 <0.001, p<0.05 等统计学表达式）
    // 但保留真正的HTML标签，使用更精确的匹配
    cleaned = cleaned.replace(/<(?=\d)/g, '&lt;') // 只转义 <数字 的情况，如 <0.001

    // 清理不被浏览器识别的自定义标签，但保留markdown格式
    cleaned = cleaned.replace(/<think[^>]*>[\s\S]*?<\/think>/gi, '') // 移除<think>标签及其内容
    cleaned = cleaned.replace(/<think[^>]*\/>/gi, '') // 移除自闭合的<think>标签

    // 更保守的HTML标签清理，避免破坏markdown格式
    cleaned = cleaned.replace(/<\/?\w+[^>]*>/g, (match) => {
      // 保留标准HTML标签，移除其他自定义标签
      const standardTags = ['p', 'div', 'span', 'a', 'img', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                           'ul', 'ol', 'li', 'table', 'tr', 'td', 'th', 'thead', 'tbody',
                           'strong', 'em', 'b', 'i', 'code', 'pre', 'blockquote', 'hr', 'br', 'style','font'];
      const tagName = match.match(/<\/?(\w+)/)?.[1]?.toLowerCase();
      return tagName && standardTags.includes(tagName) ? match : '';
    });

    // 处理转义的换行符 - 将 \n 转换为真正的换行符
    cleaned = cleaned.replace(/\\n/g, '\n');

    // 处理开头的多余空白和换行
    cleaned = cleaned.replace(/^\s*\n+/, '');

    // 关键修复：处理4个空格缩进导致的代码块问题
    // 将行首的4个空格替换为2个空格，避免被识别为代码块
    cleaned = cleaned.replace(/\n {4}/g, '\n  ');

    // 关键修复：将单个换行符转换为双换行符，以便 ReactMarkdown 正确处理段落
    // 但要避免破坏已有的双换行符和表格格式
    // 不对表格行（包含 | 字符的行）进行换行符转换
    cleaned = cleaned.replace(/(?<!\n)\n(?!\n)/g, (_, offset, string) => {
      const lines = string.substring(0, offset).split('\n');
      const currentLine = lines[lines.length - 1];
      const nextLineStart = string.substring(offset + 1).split('\n')[0];

      // 如果当前行或下一行包含表格分隔符，保持单换行
      if (currentLine.includes('|') || nextLineStart.includes('|')) {
        return '\n';
      }

      return '\n\n';
    });

    // 清理过多的连续空行（超过3个换行变为2个换行）
    cleaned = cleaned.replace(/\n{4,}/g, '\n\n\n');

    // 清理行尾多余的空格，但保留行首空格（可能是代码缩进）
    cleaned = cleaned.replace(/[ \t]+$/gm, '');

    // 清理过多的连续空格（超过4个空格变为单个空格，除非在代码块中）
    cleaned = cleaned.replace(/(?<!```[\s\S]*?)[ ]{5,}(?![\s\S]*?```)/g, ' ');

    return cleaned
  }

  // 移除复杂的状态管理，直接渲染内容以避免闪烁
  // const [visibleContent, setVisibleContent] = useState('');
  // const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // useEffect(() => {
  //   // 初始渲染部分内容（前5000字符）
  //   setVisibleContent(content.slice(0, 5000));
  //
  //   // 如果内容较长，延迟渲染剩余部分
  //   if (content.length > 5000) {
  //     if (timeoutRef.current) {
  //       clearTimeout(timeoutRef.current);
  //     }
  //
  //     timeoutRef.current = setTimeout(() => {
  //       setVisibleContent(content);
  //     }, 300);
  //   } else {
  //     setVisibleContent(content);
  //   }

  //   return () => {
  //     if (timeoutRef.current) {
  //       clearTimeout(timeoutRef.current);
  //     }
  //   };
  // }, [content]);

  // 处理文本中的LaTeX公式 - 分为内联和块级处理
  const processInlineLatex = useCallback((text: string): ReactNode[] => {
    // 只处理内联公式 $...$，避免在段落中渲染块级公式
    const parts = text.split(/(\$[^$]+?\$)/g);
    return parts.map((part, index) => {
      if (part.startsWith('$') && part.endsWith('$') && !part.startsWith('$$')) {
        try {
          return <InlineMath key={index} math={part.slice(1, -1).trim()} />;
        } catch (error) {
          return (
            <span key={index} className="latex-error">
              {part}
            </span>
          );
        }
      }
      return part;
    });
  }, []);

  // 处理块级LaTeX公式
  const processBlockLatex = useCallback((text: string): ReactNode[] => {
    // 处理块级公式 $$...$$
    const parts = text.split(/(\$\$.*?\$\$)/g);
    return parts.map((part, index) => {
      if (part.startsWith('$$') && part.endsWith('$$')) {
        try {
          return <BlockMath key={index} math={part.slice(2, -2).trim()} />;
        } catch (error) {
          return (
            <span key={index} className="latex-error">
              {part}
            </span>
          );
        }
      }
      return part;
    });
  }, []);

  // 使用useMemo优化渲染器配置
  const renderers = useMemo<Components>(() => ({
    // 段落渲染器 - 支持 Emoji 和内联LaTeX，移动端优化
    p: ({ children }) => {
      const processedChildren = React.Children.map(children, child => {
        if (typeof child === 'string') {
          // 先处理 emoji，再处理内联LaTeX（避免块级LaTeX导致DOM嵌套问题）
          const emojiProcessed = processEmojis(child);
          return processInlineLatex(emojiProcessed);
        }
        return child;
      });

      // 根据 fontSize 属性设置不同的字体大小
      const paragraphClass = fontSize === 'lg'
        ? "text-sm md:text-[16px] leading-6 md:leading-7 my-2 md:my-3 text-gray-700"
        : "text-sm md:text-[14px] leading-6 md:leading-7 my-2 md:my-3 text-gray-700";

      return (
        <p className={paragraphClass}>
          {processedChildren}
        </p>
      );
    },

    // 标题渲染器 - 移动端优化，减少突兀感
    h1: ({ children }) => (
      <h1 className="text-lg md:text-2xl font-semibold md:font-bold mt-4 md:mt-6 mb-2 md:mb-4 text-gray-900">
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-base md:text-xl font-medium md:font-semibold mt-3 md:mt-5 mb-2 md:mb-3 text-gray-800">
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-sm md:text-lg font-medium mt-3 md:mt-4 mb-1.5 md:mb-2 text-gray-800">
        {children}
      </h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-sm md:text-base font-medium mt-2.5 md:mt-4 mb-1.5 md:mb-2 text-gray-700">
        {children}
      </h4>
    ),
    h5: ({ children }) => (
      <h5 className="text-xs md:text-sm font-medium mt-2 md:mt-3 mb-1 md:mb-2 text-gray-700">
        {children}
      </h5>
    ),
    h6: ({ children }) => (
      <h6 className="text-xs md:text-xs font-medium mt-2 md:mt-3 mb-1 md:mb-2 text-gray-600">
        {children}
      </h6>
    ),

    // 文本渲染器 - 支持 Emoji 和内联LaTeX，移动端优化
    text: ({ children }) => {
      const textContent = String(children);
      // 先处理 emoji，再处理内联LaTeX
      const emojiProcessed = processEmojis(textContent);
      const processedContent = processInlineLatex(emojiProcessed);

      // 根据 fontSize 属性设置不同的字体大小
      const textClass = fontSize === 'lg'
        ? "text-sm md:text-[16px] text-gray-700"
        : "text-sm md:text-[12px] text-gray-700";

      return (
        <span className={textClass}>
          {processedContent}
        </span>
      );
    },

    // 代码块渲染器
    code: ({ children, className, ref, ...props }) => {
      const codeContent = String(children).trim();
      const match = /language-(\w+)/.exec(className || '');
      const language = match?.[1];
      const inline = (props as any)?.inline;

      // Mermaid图表处理
      if (language === 'mermaid') {
        return <SimpleMermaid code={codeContent} className="my-4" />;
      }

      // LaTeX块处理
      if (language === 'latex' || language === 'tex') {
        return (
          <ErrorBoundary fallback={
            <span className="latex-error-block block">
              <SyntaxHighlighter
                language="text"
                style={vscDarkPlus as any}
                PreTag="span"
                className="block"
              >
                {`LaTeX syntax error in block:\n\n${codeContent}`}
              </SyntaxHighlighter>
            </span>
          }>
            <BlockMath math={codeContent} />
          </ErrorBoundary>
        );
      }

      // 智能内联代码判断
      const trimmedContent = codeContent.trim();

      // 生物医学术语模式：基因名、蛋白质名、技术名称等
      const isBiologicalTerm = /^[A-Z0-9]+[A-Z0-9\-]*[A-Z0-9]*$/i.test(trimmedContent) ||
        /^(m6A|MeRIP|qPCR|RT-qPCR|RIP|IGF2BP\d*|METTL\d*|SNAI\d*|FBXO\d*|PD-L\d*)$/i.test(trimmedContent);

      // 技术方法名称模式
      const isTechnicalTerm = /^(seq|qPCR|RT-qPCR|pull-down|Polysome Profiling|Actinomycin D)$/i.test(trimmedContent);

      // 短代码判断
      const isShortCode = trimmedContent.length < 100 && !trimmedContent.includes('\n');
      const hasNoLanguage = !language;

      const isLikelyInline = inline ||
        (hasNoLanguage && isShortCode) ||
        (hasNoLanguage && (isBiologicalTerm || isTechnicalTerm) && trimmedContent.length < 30);

      // 内联代码处理 - 使用专业医学样式
      if (isLikelyInline) {
        return (
          <code
            className="inline-code"
            style={{
              fontFamily: '"SFMono-Regular", "Monaco", "Inconsolata", "Roboto Mono", monospace',
              fontSize: '0.875rem',
              backgroundColor: '#afb8c133',
              color: 'rgb(31 41 55)',
              border: '1px solid #e2e8f0',
              borderRadius: '0.25rem',
              padding: '0.125rem 0.375rem',
              display: 'inline',
              whiteSpace: 'nowrap',
              verticalAlign: 'baseline',
              margin: '0',
              lineHeight: '1'
            }}
            ref={ref}
            {...props}
          >
            {children}
          </code>
        );
      }

      // 语法高亮处理
      return (
        <SyntaxHighlighter
          style={vscDarkPlus as any}
          language={language || 'text'}
          PreTag="span"
          className="block"
          showLineNumbers={!inline && (language || '').length > 0}
          wrapLongLines
          customStyle={{
            backgroundColor: '#afb8c133',
            color: 'white',
            padding: '1rem',
            borderRadius: '0.375rem',
            border: '1px solid #e2e8f0'
          }}
          {...props}
        >
          {codeContent.includes("【--Final content--】") ? codeContent.split("【--Final content--】")[1] : codeContent}
        </SyntaxHighlighter>
      );
    },

    // 列表渲染器 - 移动端优化
    ul: ({ children }) => (
      <ul className="list-disc list-inside my-2 md:my-3 ml-2 md:ml-4 space-y-1 md:space-y-1.5 text-sm md:text-base text-gray-700">
        {children}
      </ul>
    ),
    ol: ({ children }) => (
      <ol className="list-decimal list-inside my-2 md:my-3 ml-2 md:ml-4 space-y-1 md:space-y-1.5 text-sm md:text-base text-gray-700">
        {children}
      </ol>
    ),
    li: ({ children }) => (
      <li className="leading-5 md:leading-6 mb-1 md:mb-1.5">
        {children}
      </li>
    ),

    // 引用块渲染器 - 移动端优化
    blockquote: ({ children }) => (
      <blockquote className="border-l-2 md:border-l-4 border-blue-200 pl-3 md:pl-4 py-1 md:py-2 my-2 md:my-4 bg-blue-50 text-sm md:text-base text-gray-600 italic">
        {children}
      </blockquote>
    ),

    // 表格渲染器 - 移动端滚动优化，修复DOM嵌套问题
    table: ({ children }) => (
      <span className="table-container block overflow-x-auto my-2 md:my-4 -mx-2 md:mx-0">
        <table className="min-w-full text-xs md:text-sm border-collapse">
          {children}
        </table>
      </span>
    ),
    thead: ({ children }) => (
      <thead className="bg-gray-50">
        {children}
      </thead>
    ),
    tbody: ({ children }) => (
      <tbody>
        {children}
      </tbody>
    ),
    tr: ({ children }) => (
      <tr>
        {children}
      </tr>
    ),
    th: ({ children }) => (
      <th className="border border-gray-200 px-2 md:px-3 py-1 md:py-2 text-left font-medium text-gray-700 text-xs md:text-sm">
        {children}
      </th>
    ),
    td: ({ children }) => (
      <td className="border border-gray-200 px-2 md:px-3 py-1 md:py-2 text-gray-600 text-xs md:text-sm">
        {children}
      </td>
    ),

    // 图片渲染器 - 移动端优化，修复DOM嵌套问题
    img: ({ src, alt, ...props }) => (
      <span className="image-container block my-2 md:my-4">
        <img
          src={src}
          alt={alt || 'Image'}
          loading="lazy"
          className="max-w-full h-auto rounded-md md:rounded-lg"
          {...props}
        />
        {alt && (
          <span className="image-caption block text-xs md:text-sm text-gray-500 text-center mt-1 md:mt-2 italic">
            {alt}
          </span>
        )}
      </span>
    ),

    // 链接渲染器 - 移动端优化
    a: ({ href, children, ...props }) => (
      <a
        href={href}
        className="text-blue-600 hover:text-blue-800 underline text-sm md:text-base break-words"
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    ),

    // 强调文本渲染器 - 移动端优化
    strong: ({ children }) => (
      <strong className="font-semibold text-gray-900">
        {children}
      </strong>
    ),
    em: ({ children }) => (
      <em className="italic text-gray-700">
        {children}
      </em>
    ),

    // HR分割线渲染器 - 移动端优化
    thematicBreak: () => (
      <hr className="ai-response-hr border-gray-200 my-3 md:my-4" />
    )
  }), [processInlineLatex, fontSize]);

  // 根据 fontSize 属性设置字体大小类名
  const fontSizeClass = fontSize === 'lg' ? 'text-base' : fontSize === 'base' ? 'text-sm' : 'text-xs'

  // 处理内容：先清理，再处理样式
  const processedContent = useMemo(() => {
    const cleaned = cleanContent(content);
    return processContent(cleaned);
  }, [content, processContent]);

  return (
    <div className={`ai-response-container max-w-4xl ${fontSizeClass}`}>
      <ErrorBoundary fallback={
        <span className="render-error block">
          Failed to render content. The response may contain unsupported formatting.
        </span>
      }>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={renderers}
          skipHtml={false}
        >
          {processedContent}
        </ReactMarkdown>
      </ErrorBoundary>
    </div>
  );
};

export default React.memo(AiResponseRenderer);