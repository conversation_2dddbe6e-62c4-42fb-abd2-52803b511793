import type React from 'react'
import { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import NotFound from './NotFound'
import { AppService } from '../utils/appService'
import { getCurrentLanguage, addLanguageChangeListener, removeLanguageChangeListener } from '../i18n/simple'

interface AppRouteGuardProps {
  children: React.ReactNode
}

/**
 * 应用路由守卫组件
 * 验证URL中的应用名称是否有效，如果无效则显示404页面
 *
 * 特殊处理：
 * - knowledgebase路径：完全独立于应用验证系统，不调用任何API，直接通过
 * - 其他应用路径：基于API实际应用列表进行动态验证
 */
const AppRouteGuard: React.FC<AppRouteGuardProps> = ({ children }) => {
  const { appName } = useParams<{ appName: string }>()
  const [isValidApp, setIsValidApp] = useState<boolean | null>(null) // null表示正在验证
  const [isLoading, setIsLoading] = useState(true)
  const [currentLanguage, setCurrentLanguage] = useState(getCurrentLanguage())

  // knowledgebase路径的快速检查 - 避免不必要的状态管理
  if (appName === 'knowledgebase') {
    console.log('AppRouteGuard: knowledgebase路径，完全跳过验证系统')
    return <>{children}</>
  }

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChange = (newLang: string) => {
      console.log('AppRouteGuard: 检测到语言变化', { from: currentLanguage, to: newLang })
      setCurrentLanguage(newLang as any)
      // 语言变化时重置验证状态，触发重新验证
      setIsValidApp(null)
      setIsLoading(true)
    }

    addLanguageChangeListener(handleLanguageChange)

    return () => {
      removeLanguageChangeListener(handleLanguageChange)
    }
  }, [currentLanguage])

  useEffect(() => {
    const validateApp = async () => {
      // 注意：knowledgebase路径已经在组件顶部被处理，不会到达这里

      if (!appName) {
        console.log('AppRouteGuard: 应用名称为空，显示404页面')
        setIsValidApp(false)
        setIsLoading(false)
        return
      }

      console.log(`AppRouteGuard: 开始验证应用 "${appName}"，当前语言: ${currentLanguage}`)

      // 对所有非knowledgebase应用进行API验证
      try {
        const appService = AppService.getInstance()
        const isValid = await appService.isValidAppName(appName)

        console.log(`AppRouteGuard: 应用验证结果`, {
          appName,
          currentLanguage,
          isValid,
          action: isValid ? '允许访问' : '显示404页面'
        })

        setIsValidApp(isValid)
      } catch (error) {
        console.error('AppRouteGuard: 应用验证失败', {
          appName,
          currentLanguage,
          error: error instanceof Error ? error.message : String(error),
          action: '显示404页面'
        })
        // 验证失败时，为了安全起见显示404
        setIsValidApp(false)
      } finally {
        setIsLoading(false)
      }
    }

    validateApp()
  }, [appName, currentLanguage]) // 添加currentLanguage作为依赖

  // 正在验证中，显示空白（避免闪烁）
  if (isLoading) {
    console.log('AppRouteGuard: 正在验证中，显示空白页面')
    return null
  }

  // 应用名称无效，显示404页面
  if (isValidApp === false) {
    console.log(`AppRouteGuard: 应用 "${appName}" 验证失败，渲染404页面`)
    return <NotFound />
  }

  // 应用名称有效，渲染子组件
  if (isValidApp === true) {
    console.log(`AppRouteGuard: 应用 "${appName}" 验证通过，渲染子组件`)
    return <>{children}</>
  }

  // 异常情况：isValidApp为null但isLoading为false
  console.error('AppRouteGuard: 异常状态', { appName, isValidApp, isLoading })
  return <NotFound />
}

export default AppRouteGuard
