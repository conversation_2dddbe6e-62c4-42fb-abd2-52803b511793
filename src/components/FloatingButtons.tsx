import type React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { PlusIcon, HomeIcon } from './icons/Icons';
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks';

interface FloatingButtonsProps {
  showNewChat?: boolean // 是否显示新建会话按钮，默认为true
}

/**
 * 悬浮按钮组件 - 显示在屏幕左侧的固定位置
 * 包含新建会话和返回首页两个功能按钮
 */
const FloatingButtons: React.FC<FloatingButtonsProps> = ({ showNewChat = false }) => {
  const { t } = useSimpleTranslation()
  const { currentLanguage, navigateToApp } = useI18nRouter()
  const navigate = useNavigate();
  const { appName } = useParams<{ appName?: string }>();

  // 获取当前应用名，如果无法从URL获取则使用默认值
  const currentAppName = appName || 'novax-base';

  // 返回首页并开启新会话 - 根据当前语言和应用跳转
  const handleNewChat = () => {
    navigateToApp(currentAppName);
  };

  // 返回首页 - 根据当前语言和应用跳转
  const handleGoHome = () => {
    navigateToApp(currentAppName);
  };

  return (
    <>
      {/* PC端悬浮按钮 - 左侧固定位置 */}
      <div className="hidden md:flex fixed left-6 top-1/2 transform -translate-y-1/2 z-50 flex-col gap-4">
        {/* 新建会话按钮 - 根据showNewChat prop条件渲染 */}
        {showNewChat && (
          <button
            onClick={handleNewChat}
            className="
              relative w-14 h-14
              bg-gradient-to-br from-gray-100/90 to-gray-200/90
              hover:from-gray-200/95 hover:to-gray-300/95
              text-gray-600 hover:text-gray-700 rounded-2xl
              shadow-sm hover:shadow-md
              backdrop-blur-md bg-opacity-95
              flex items-center justify-center
              transition-all duration-300 ease-out
              hover:scale-105 hover:-translate-y-1
              active:scale-95 active:translate-y-0
              group border border-gray-200/50
              before:absolute before:inset-0 before:rounded-2xl
              before:bg-gradient-to-br before:from-white/30 before:to-transparent
              before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300
            "
            title={t('floating.newChat')}
          >
            <PlusIcon
              width={22}
              height={22}
              stroke="currentColor"
              strokeWidth={2.5}
              className="relative z-10 group-hover:scale-110 group-hover:rotate-90 transition-all duration-300"
            />
          </button>
        )}
      </div>

      {/* 移动端返回首页按钮 - 右下角固定位置 */}
      <button
        onClick={handleGoHome}
        className="
          md:hidden
          fixed bottom-[50%] left-[5%] z-50
          relative w-14 h-14
          bg-gradient-to-br from-gray-50/90 to-gray-100/90
          hover:from-gray-100/95 hover:to-gray-200/95
          text-gray-500 hover:text-gray-600 rounded-2xl
          shadow-sm hover:shadow-md
          backdrop-blur-md bg-opacity-95
          flex items-center justify-center
          transition-all duration-300 ease-out
          hover:scale-105 hover:-translate-y-1
          active:scale-95 active:translate-y-0
          group border border-gray-200/50
          before:absolute before:inset-0 before:rounded-2xl
          before:bg-gradient-to-br before:from-white/30 before:to-transparent
          before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300
        "
        title={t('floating.goHome')}
      >
        <HomeIcon
          width={22}
          height={22}
          stroke="currentColor"
          strokeWidth={2.5}
          className="relative z-10 group-hover:scale-110 transition-all duration-300"
        />
      </button>
    </>
  );
};

export default FloatingButtons;