import type React from 'react';
import { useState, useEffect } from 'react';
import { useCompetition } from '../contexts/CompetitionContext';
import CompetitionCongratulationModal from './competition/CompetitionCongratulationModal';

const GlobalCompetitionCongratulation: React.FC = () => {
  const {
    hasCompetitionPermission,
    isPermissionLoading,
    setTimeExpiredCallback
  } = useCompetition();

  // 祝贺弹窗状态
  const [showCongratulationModal, setShowCongratulationModal] = useState(false);

  // 处理时间到期
  const handleTimeExpired = () => {
    console.log('比赛时间到期，显示祝贺弹窗');
    setShowCongratulationModal(true);
  };

  // 设置时间到期回调
  useEffect(() => {
    if (hasCompetitionPermission && !isPermissionLoading) {
      setTimeExpiredCallback(handleTimeExpired);
    }
    
    // 清理函数
    return () => {
      if (hasCompetitionPermission && !isPermissionLoading) {
        setTimeExpiredCallback(() => {});
      }
    };
  }, [hasCompetitionPermission, isPermissionLoading, setTimeExpiredCallback]);

  // 如果没有权限或权限检查中，不显示任何组件
  if (isPermissionLoading || !hasCompetitionPermission) {
    return null;
  }

  return (
    <>
      {/* 祝贺弹窗 */}
      <CompetitionCongratulationModal
        open={showCongratulationModal}
        onClose={() => setShowCongratulationModal(false)}
      />
    </>
  );
};

export default GlobalCompetitionCongratulation;
