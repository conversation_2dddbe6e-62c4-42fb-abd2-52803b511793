import type React from 'react';
import { useState, useMemo } from 'react';
import { useCompetition } from '../contexts/CompetitionContext';
import { useSimpleTranslation } from '../i18n/simple-hooks';
import CompetitionSubmissionModal from './competition/CompetitionSubmissionModal';
import CompetitionSuccessModal from './competition/CompetitionSuccessModal';
import { XAiApi } from '../api/src/xai-api';
import { getApiConfig } from '../utils/apiConfig';

const GlobalCompetitionTimer: React.FC = () => {
  const {
    competitionStarted,
    competitionCompleted,
    competitionTimeRemaining,
    getProgress,
    hasCompetitionPermission,
    isPermissionLoading,
    isStateInitialized
  } = useCompetition();
  const { t } = useSimpleTranslation();

  // 模态框状态
  const [showSubmissionModal, setShowSubmissionModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // 创建XAiApi实例
  const xAiApi = useMemo(() => {
    const apiConfig = getApiConfig();
    return new XAiApi({
      user: apiConfig.user,
      apiBase: apiConfig.apiBase,
      medxyToken: apiConfig.medxyToken
    });
  }, []);

  // 处理提交作品按钮点击
  const handleSubmitWork = () => {
    setShowSubmissionModal(true);
  };

  // 处理提交成功
  const handleSubmissionSuccess = () => {
    setShowSubmissionModal(false);
    setShowSuccessModal(true);
    // 不立即结束比赛，等用户关闭问卷后再结束
  };

  // 处理填写调研问卷
  const handleFillSurvey = () => {
    // 这里可以打开调研问卷链接
    location.href = "https://www.medsci.cn/form/detail.do?id=2c03595d9"
  };

  // 调试日志
  console.log('🎯 GlobalCompetitionTimer 渲染状态:', {
    isPermissionLoading,
    hasCompetitionPermission,
    isStateInitialized,
    competitionStarted,
    competitionCompleted,
    competitionTimeRemaining
  });

  // 1. 权限检查优先级最高
  if (isPermissionLoading || !hasCompetitionPermission) {
    // 权限检查中或没有权限，不显示任何组件
    console.log('🚫 GlobalCompetitionTimer: 权限检查中或用户没有比赛权限，不显示组件');
    return null;
  }

  // 2. 状态初始化检查
  if (!isStateInitialized) {
    // 状态还未初始化，显示状态恢复中
    return (
      <div className="fixed top-[4rem] right-4 z-50 max-w-xs">
        <div className="bg-white rounded-2xl shadow-2xl p-4 border-2 border-blue-500 bg-blue-50">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mr-2"></div>
              <span className="text-blue-700 font-medium text-sm">正在恢复状态...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 3. 比赛状态检查
  if (!competitionStarted) {
    // 比赛没有开始，不显示任何组件
    console.log('📝 GlobalCompetitionTimer: 比赛未开始，不显示组件');
    return null;
  }

  if (competitionCompleted) {
    // 比赛已完成，不显示倒计时器
    console.log('🏁 GlobalCompetitionTimer: 比赛已完成，不显示倒计时器');
    return null;
  }

  // 4. 显示倒计时器
  console.log('⏰ GlobalCompetitionTimer: 显示倒计时器，剩余时间:', competitionTimeRemaining);

  // 将秒数转换为时分秒
  const hours = Math.floor(competitionTimeRemaining / 3600);
  const minutes = Math.floor((competitionTimeRemaining % 3600) / 60);
  const seconds = competitionTimeRemaining % 60;

  // 判断是否时间紧急（少于5分钟）
  const isUrgent = competitionTimeRemaining <= 300; // 5分钟
  const isCritical = competitionTimeRemaining <= 60; // 1分钟

  // 获取进度百分比
  const progress = getProgress();

  return (
    <div className="fixed top-[4rem] right-4 z-50 max-w-xs">
      <div className={`
        bg-white rounded-2xl shadow-2xl p-4 md:p-6 border-2 transition-all duration-300
        ${isCritical ? 'border-red-500 bg-red-50' : isUrgent ? 'border-orange-500 bg-orange-50' : 'border-blue-500 bg-blue-50'}
      `}>
        {/* 标题 */}
        <div className="text-center mb-3 md:mb-4">
          <h3 className={`text-base md:text-lg font-bold mb-1 md:mb-2 ${isCritical ? 'text-red-700' : isUrgent ? 'text-orange-700' : 'text-blue-700'}`}>
            {t('competition.competitionInProgress')}
          </h3>
          <p className={`text-xs md:text-sm ${isCritical ? 'text-red-600' : isUrgent ? 'text-orange-600' : 'text-blue-600'}`}>
            {t('competition.timeRemaining')}
          </p>
        </div>

        {/* 时间显示 */}
        <div className="text-center mb-3 md:mb-4">
          <div className={`
            text-2xl md:text-4xl font-bold mb-1 md:mb-2 font-mono
            ${isCritical ? 'text-red-600 animate-pulse' : isUrgent ? 'text-orange-600' : 'text-blue-600'}
          `}>
            {String(hours).padStart(2, '0')}:{String(minutes).padStart(2, '0')}:{String(seconds).padStart(2, '0')}
          </div>
          <div className={`text-xs md:text-sm ${isCritical ? 'text-red-500' : isUrgent ? 'text-orange-500' : 'text-blue-500'}`}>
            {hours} {t('competition.hours')} {minutes} {t('competition.minutes')} {seconds} {t('competition.seconds')}
          </div>
        </div>

        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div 
            className={`
              h-2 rounded-full transition-all duration-1000
              ${isCritical ? 'bg-red-500' : isUrgent ? 'bg-orange-500' : 'bg-blue-500'}
            `}
            style={{ 
              width: `${Math.max(0, progress)}%` 
            }}
          ></div>
        </div>

        {/* 提交作品按钮 */}
        <button
          onClick={handleSubmitWork}
          className={`
            w-full py-2 px-3 md:px-4 rounded-lg text-white font-medium text-xs md:text-sm transition-colors
            ${isCritical ? 'bg-red-600 hover:bg-red-700' : isUrgent ? 'bg-orange-600 hover:bg-orange-700' : 'bg-blue-600 hover:bg-blue-700'}
          `}
        >
          {t('competition.submitWork')}
        </button>

        {/* 时间到提示 */}
        {competitionTimeRemaining <= 0 && (
          <div className="text-center text-red-600 font-bold animate-pulse mt-2">
            {t('competition.timeUp')}
          </div>
        )}
      </div>

      {/* 提交作品模态框 */}
      <CompetitionSubmissionModal
        open={showSubmissionModal}
        onClose={() => setShowSubmissionModal(false)}
        onSubmitSuccess={handleSubmissionSuccess}
        xAiApi={xAiApi}
      />

      {/* 成功提示模态框 */}
      <CompetitionSuccessModal
        open={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          // 移除关闭问卷时结束比赛的逻辑，让用户可以继续使用
        }}
        onFillSurvey={handleFillSurvey}
      />
    </div>
  );
};

export default GlobalCompetitionTimer;
