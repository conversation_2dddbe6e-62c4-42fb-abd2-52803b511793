import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import MainContent from './MainContent';
import ConversationsPage from './ConversationPage';
import Pay from './pay/Pay';
import PayMobile from './pay/PayMobile';
import { useCompetition } from '../contexts/CompetitionContext';

import SEOHead from './SEOHead';
import ArtifactsDrawer from './artifacts/ArtifactsDrawer';
import type { DifyApi, IGetAppParametersResponse } from '../api/src/dify-api';
import type { XAiApi, IGetAiAppInfoResponse, PackageByKey, FeeType } from '../api/src/xai-api';
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks';
import { isMobile } from '../api/src/utils';
import Cookies from 'js-cookie';
import { message } from 'antd';
import { ApiTimingManager } from '../utils/apiTimingManager';
import { ApiCallDeduplicator } from '../utils/apiCallDeduplicator';
import { getDomainForConfigKey } from '../utils/domainUtils';
import NotFound from './NotFound';
import { useRootAccess } from './I18nRouteWrapper';
import { AppService } from '../utils/appService';
import { useCookieMonitor } from '../hooks/useCookieMonitor';

interface ApiProps {
  difyApi: DifyApi
  xAiApi: XAiApi
  currentAppUuid: string
  setCurrentAppUuid: React.Dispatch<React.SetStateAction<string>>
  user: string
}

const Home: React.FC<ApiProps> = ({ difyApi, xAiApi,
  currentAppUuid, setCurrentAppUuid, user }) => {
  const { t } = useSimpleTranslation()
  const { navigateToApp, currentLanguage } = useI18nRouter()

  // 应用列表状态
  const [appList, setAppList] = useState<IGetAiAppInfoResponse[]>([])
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    // PC端默认显示侧边栏，移动端默认隐藏
    return window.innerWidth >= 768; // md断点是768px
  });
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [conversationModalOpen, setConversationModalOpen] = useState(false);
  const [appParam, setAppParam] = useState<IGetAppParametersResponse>()
  const [appListSub, setAppListSub] = useState<Map<string, PackageByKey | null>>(new Map())
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false)

  // Artifacts drawer state - only for datascore-base
  const [artifactsDrawerOpen, setArtifactsDrawerOpen] = useState(false)
  const [artifactsDrawerWidth, setArtifactsDrawerWidth] = useState(50) // Default 50% width
  const [isArtifactsResizing, setIsArtifactsResizing] = useState(false)

  // 支付相关状态
  const [payUrl, setPayUrl] = useState('')
  const [paymentLoading, setPaymentLoading] = useState(false)
  const [autoSubscribing, setAutoSubscribing] = useState(false) // 自动订阅状态
  const timeRef = useRef<NodeJS.Timeout | null>(null)

  // 使用全局比赛状态
  const { startCompetition, hasCompetitionPermission } = useCompetition()



  const { appName: urlAppName } = useParams();
  const navigate = useNavigate();

  // 获取根域名访问状态
  const { isRootAccess, currentAppName: rootAppName } = useRootAccess();

  // 确定当前应用名称：根域名访问时使用Context中的应用名，否则使用URL参数
  const appName = isRootAccess ? rootAppName : urlAppName;

  // 简化的新对话检测逻辑
  // 由于我们现在使用临时URL（如 new-chat-timestamp），Home组件不再需要处理新对话
  // 新对话会直接路由到ChatDetail组件

  // 获取应用列表（使用AppService避免重复调用）
  useEffect(() => {
    const lang = currentLanguage || 'zh';
    Cookies.set("ai_apps_lang", lang, {domain: '.medsci.cn'});
    Cookies.set("ai_apps_lang", lang, {domain: '.medon.com.cn'});

    const fetchAppList = async () => {
      try {
        console.log('🏠 Home: 使用AppService获取应用列表，避免重复API调用', {
          currentLanguage: lang,
          timestamp: new Date().toISOString()
        });

        const appService = AppService.getInstance();
        const apps = await appService.getAppList();

        console.log('🏠 Home: AppService返回应用列表', {
          appCount: apps.length,
          firstApp: apps[0]?.appNameEn
        });

        setAppList(apps);
        setCurrentAppUuid(apps[0]?.appUuid || '');
      } catch (error) {
        console.error('🏠 Home: 获取应用列表失败:', error);
      }
    };

    fetchAppList();
  }, [currentLanguage])

  // 监听语言变化，更新订阅状态
  useEffect(() => {
    if (appList.length > 0) {
      console.log('语言变化，重新获取订阅状态:', currentLanguage);
      Cookies.set("ai_apps_lang", currentLanguage, {domain: '.medsci.cn'});
      Cookies.set("ai_apps_lang", currentLanguage, {domain: '.medon.com.cn'});
      fetchAllAppSubscriptions();
    }
  }, [currentLanguage])

  // 更新API配置
  useEffect(() => {
    const medxyToken = Cookies.get('medxyToken')
    if (user && medxyToken) {
      console.log('Updated user====', user)
      xAiApi.updateOptions({
        user: user,
        apiBase: xAiApi.options.apiBase,
        medxyToken: medxyToken
      });
      difyApi.updateOptions({
        user: user,
        apiBase: difyApi.options.apiBase,
        medxyToken: medxyToken,
        appId: currentApp?.dAppUuid
      });
    }
  }, [user, currentAppUuid, appList, xAiApi, difyApi]) // 依赖 user, currentAppUuid, appList

  // ESC键关闭弹窗功能
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // 按优先级关闭弹窗：会话历史 > 搜索 > 侧边栏
        if (conversationModalOpen) {
          setConversationModalOpen(false)
        } else if (searchModalOpen) {
          setSearchModalOpen(false)
        } else if (sidebarOpen) {
          const isDesktop = window.innerWidth >= 768;
          if (isDesktop) {
            // PC端：折叠侧边栏，效果和点击菜单图标一样
            setSidebarCollapsed(true);
          } else {
            // 移动端：完全关闭侧边栏
            setSidebarOpen(false);
          }
        }
      }
    }

    // 只在有弹窗打开时添加监听器
    if (conversationModalOpen || searchModalOpen || sidebarOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => {
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [conversationModalOpen, searchModalOpen, sidebarOpen])

  // 监听窗口大小变化，调整侧边栏状态
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 768
      // 如果从移动端切换到PC端，自动显示侧边栏并重置折叠状态
      if (isDesktop && !sidebarOpen) {
        setSidebarOpen(true)
        setSidebarCollapsed(false)
      }
      // 如果从PC端切换到移动端，自动隐藏侧边栏并重置折叠状态
      else if (!isDesktop && sidebarOpen) {
        setSidebarOpen(false)
        setSidebarCollapsed(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [sidebarOpen])

  const login = async() => {
    await xAiApi.logout()
    window.sessionStorage.setItem('redirectUrl', window.location.href)
    if (!currentLanguage || currentLanguage === 'zh') {
		  // 为了解决 TypeScript 类型错误，需要先声明 window.addLoginDom 方法
		  if (window.location.origin.includes('medxy.ai')) {
        (window as any).top.location.href = location.origin + '/' + currentLanguage + "/login"
      } else {
        (window as any).addLoginDom?.()
      }
		} else {
		  (window as any).top.location.href =  location.origin + '/' + currentLanguage + "/login"
		}
  };

  const preCheckLogin = () => {
    const medxyToken = Cookies.get('medxyToken')
    if (!medxyToken) {
      console.log('触发登录')
      login()
      return false
    }
    return true
  }

  // 前置检查函数
  const preCheck = () => {
    if (!preCheckLogin()) {
      return false
    }
    // 直接使用已经计算好的 currentApp，而不是重新查找
    if (!currentApp?.appUser || currentApp?.appUser?.status !== '1') {
      console.log('preCheck==用户未支付，检查是否有免费套餐', currentApp)

      // 检查是否有免费套餐，如果有则直接自动订阅
      const subStatusDetail = currentApp ? appListSub.get(currentApp.appNameEn) : null
      const firstPackage = subStatusDetail?.feeTypes?.[0]

      if (firstPackage && (firstPackage.type === '免费')) {
        // 防止重复触发自动订阅
        if (autoSubscribing) {
          console.log('preCheck==正在自动订阅中，请等待...')
          return false
        }

        console.log('preCheck==检测到免费套餐，直接触发自动订阅，不显示弹框')
        // 直接设置自动订阅状态，不显示弹框
        setAutoSubscribing(true)

        // 直接调用订阅函数，不弹出Pay组件
        subscribe(firstPackage).then(() => {
          console.log('preCheck==免费套餐自动订阅完成')
          setAutoSubscribing(false)
        }).catch((error) => {
          console.error('preCheck==免费套餐自动订阅失败:', error)
          setAutoSubscribing(false)
          // 如果自动订阅失败，弹出支付弹框
          setShowSubscriptionModal(true)
        })

        return false
      } else {
        console.log('preCheck==没有免费套餐，触发支付弹框',firstPackage)
        setShowSubscriptionModal(true)
        return false
      }
    }
    console.log('已登录，已支付')
    return true
  };

  // 清除支付定时器的函数
  const clearPaymentTimer = () => {
    if (timeRef.current) {
      clearInterval(timeRef.current)
      timeRef.current = null
      console.log('支付状态轮询定时器已清除')
    }
  }

  // 开始比赛流程 - 只有在有权限时才允许开始
  const handleStartCompetition = () => {
    if (hasCompetitionPermission) {
      startCompetition(5400) // 开始90分钟的比赛
    } else {
      console.warn('用户没有比赛权限，无法开始比赛');
    }
  }

  // 跳转到调研问卷
  const handleShowSurvey = () => {
    // 直接打开调研问卷链接
    location.href = "https://www.medsci.cn/form/detail.do?id=2c03595d9"
  }

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearPaymentTimer()
    }
  }, [])

  // 查询支付状态
  const getStatus = async (piId: string) => {
    clearPaymentTimer() // 使用统一的清除函数

    timeRef.current = setInterval(async () => {
      const res = await xAiApi.getSubOrder({ piId })
      if (res.payStatus === 'PAID') {
        location.reload()
        clearPaymentTimer() // 使用统一的清除函数
      }
    }, 2000)
  }

  // 支付方法
  const subscribe = async (feeType: FeeType) => {
    console.log('开始支付subscribe:', feeType)
    clearPaymentTimer() // 使用统一的清除函数
    if (!preCheckLogin()) {
      console.log('=====', '用户未登录')
      return
    }
    if (!currentApp || !feeType) {
      console.log('=====', '当前应用信息不存在')
      return
    }
    if (paymentLoading) {
      console.log('===支付中')
      return
    }
    const feeSub = appListSub.get(feeType.packageKey)
    if (feeSub?.subStatus === 1 || feeSub?.subStatus === 3) {
        console.log('===已订阅', feeSub)
        return
    }
    const sub = appListSub.get(currentApp.appNameEn)
    if (feeType.type == "免费") {
      if (sub?.subStatus === 1) {
        console.log('===已订阅', currentApp)
        return
      }
    } else {
      if ((sub?.subStatus === 1 && sub?.packageType !== '免费') || sub?.subStatus === 3) {
        console.log('===已订阅', currentApp)
        return
      }
    }

    try {
      setPaymentLoading(true)
      const subscriptionParams = {
        appUuid: currentApp.appUuid || "",
        priceId: feeType.priceId,
        monthNum: feeType.monthNum,
        packageKey: feeType.packageKey,
        packageType: feeType.type
      };
      const payInfo = await xAiApi.createSubscription(subscriptionParams);
      if (feeType.type == "免费" || !feeType.feePrice) {
        console.log('===自动订阅', payInfo);

        // 免费订阅成功后，并行更新订阅状态和应用信息
        try {
          const [, updatedAppInfo] = await Promise.all([
            fetchAllAppSubscriptions(),
            xAiApi.getAppByUuid({ appUuid: currentApp.appUuid })
          ]);

          setAppList(prevAppList =>
            prevAppList.map(app =>
              app.appUuid === currentApp.appUuid ? updatedAppInfo : app
            )
          );
        } catch (error) {
          console.error('更新订阅状态或应用信息失败:', error);
        }

        // 短暂延时避免重复订阅
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        if (feeType.coinType == '人民币') {
          if (isMobile()) {
            // 移动端使用支付宝支付
            const resUrl = await xAiApi.createAliSub(JSON.parse(payInfo));
            console.log('------resUrl', resUrl);
            message.success(t('subscription.subscribeSuccess'));
            setTimeout(() => {
              window.location.href = resUrl;
            }, 1000);
          } else {
            // 桌面端使用二维码支付
            const encodedPayInfo = encodeURIComponent(payInfo)
            const newPayUrl = `${window.location.origin}/payLink/${encodedPayInfo}`
            setPayUrl(newPayUrl)
            const piId = JSON.parse(payInfo).piId
            await getStatus(piId);
          }
        } else {
          // 非中文跳转到stripe支付
          setTimeout(() => {
            window.open(payInfo, '_blank');
          }, 500);
        }
      }
    } catch (error) {
      console.error(t('subscription.paymentFailed'), error)
    } finally {
      setPaymentLoading(false)
    }
  }

  // 确定当前应用UUID的逻辑 - 修复：当应用在当前语言环境不存在时返回null
  const effectiveAppUuid = useMemo(() => {
    if (appName) {
      const foundApp = appList.find(app => app.appNameEn === appName);
      if (foundApp) {
        return foundApp.appUuid;
      }
      // 只有在应用列表已经加载完成（不为空）且应用确实不存在时才返回null
      // 如果应用列表还没有加载完成，返回空字符串等待加载
      if (appList.length > 0) {
        console.log('Home: 应用在当前语言环境不存在', {
          requestedApp: appName,
          availableApps: appList.map(app => app.appNameEn),
          action: '将显示404页面'
        });
        return null;
      }
      // 应用列表还没有加载完成，返回空字符串等待
      return '';
    }
    return appList.length > 0 ? appList[0].appUuid : '';
  }, [appName, appList]);

  useEffect(() => {
    if (effectiveAppUuid && effectiveAppUuid !== currentAppUuid) {
      setCurrentAppUuid(effectiveAppUuid);
    }
  }, [effectiveAppUuid, currentAppUuid, setCurrentAppUuid]);

  const currentApp = useMemo(() => {
    return appList.find(app => app.appUuid === effectiveAppUuid)
  }, [appList, effectiveAppUuid])

  // 获取应用信息和订阅状态 - 添加防重复机制
  useEffect(() => {
    if (!currentApp) return;

    // 防止重复调用：如果已经有相同应用的参数，则跳过
    if (appParam && difyApi.options.appId === currentApp.dAppUuid) {
      console.log('🏠 Home.tsx - 跳过重复调用，已有相同应用的参数', {
        currentAppId: currentApp.dAppUuid,
        cachedAppId: difyApi.options.appId
      });
      return;
    }

    const fetchAppInfo = async () => {
      const medxyToken = Cookies.get('medxyToken') || '';

      difyApi.updateOptions({
        user: difyApi.options.user,
        apiBase: difyApi.options.apiBase,
        medxyToken: medxyToken,
        appId: currentApp.dAppUuid
      });

      console.log('🏠 Home.tsx - 调用 getAppParameters 接口', {
        appId: currentApp.dAppUuid,
        appName: currentApp.appName,
        timestamp: new Date().toISOString()
      });
      const appInfo = await difyApi.getAppParameters();
      setAppParam(appInfo);
      console.log('🏠 Home.tsx - getAppParameters 调用完成', appInfo);
    };

    fetchAppInfo();
  }, [currentApp, appParam]);

  // ESC键关闭弹窗功能
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // 按优先级关闭弹窗：会话历史 > 搜索 > artifacts drawer > 侧边栏
        if (conversationModalOpen) {
          setConversationModalOpen(false)
        } else if (searchModalOpen) {
          setSearchModalOpen(false)
        } else if (artifactsDrawerOpen) {
          setArtifactsDrawerOpen(false)
        } else if (sidebarOpen) {
          const isDesktop = window.innerWidth >= 768;
          if (isDesktop) {
            // PC端：折叠侧边栏
            setSidebarCollapsed(true);
          } else {
            // 移动端：完全关闭侧边栏
            setSidebarOpen(false);
          }
        }
      }
    }

    // 只在有弹窗打开时添加监听器
    if (conversationModalOpen || searchModalOpen || artifactsDrawerOpen || sidebarOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => {
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [conversationModalOpen, searchModalOpen, artifactsDrawerOpen, sidebarOpen]);

  // 获取所有app的订阅状态和应用配置（使用API时序管理器）
  const fetchAllAppSubscriptions = async (forceRefresh = false) => {
    if (appList.length === 0) return;

    try {
      console.log('🔄 开始获取所有应用的订阅状态和应用配置');

      const configKey = getDomainForConfigKey();
      console.log('📡 准备并行调用getPackageByDomain和getAppByDomain接口', {
        domain: configKey,
        tokenInfo: ApiTimingManager.getTokenInfo()
      });

      // 获取订阅状态（只调用一个接口，应用配置使用AppService缓存）
      const subscriptionMapData = await ApiTimingManager.safeGetPackageByDomain(
        () => ApiCallDeduplicator.getPackageByDomainDeduped(
          configKey,
          () => xAiApi.getPackageByDomain({ configKey }),
          forceRefresh // 传递强制刷新参数
        )
      );

      // 使用AppService的缓存数据而不是重复调用getAppByDomain API
      // 这样可以避免与AppRouteGuard的重复调用
      const appService = AppService.getInstance();
      const updatedAppList = await appService.getAppList(forceRefresh);

      console.log('🔄 Home: 使用AppService缓存避免重复API调用', {
        appCount: updatedAppList.length,
        cacheStatus: appService.getCacheStatus()
      });

      // 处理订阅状态数据
      const subscriptionMap = new Map<string, PackageByKey | null>();
      for (const appNameEn in subscriptionMapData) {
        if (Object.prototype.hasOwnProperty.call(subscriptionMapData, appNameEn)) {
          subscriptionMap.set(appNameEn, subscriptionMapData[appNameEn]);
        }
      }

      // 更新应用列表和订阅状态
      setAppList(updatedAppList);
      setAppListSub(subscriptionMap);

      // 如果当前应用UUID为空，设置为第一个应用
      if (!currentAppUuid && updatedAppList.length > 0) {
        setCurrentAppUuid(updatedAppList[0].appUuid);
      }

      console.log('✅ 订阅状态和应用配置获取成功', {
        subscriptionCount: subscriptionMap.size,
        appCount: updatedAppList.length
      });
    } catch (error: any) {
      console.error('❌ 批量获取订阅状态和应用配置失败:', error);

      // 如果是token相关错误，说明用户未登录，这是正常情况
      if (error?.message === 'No authentication token available') {
        console.log('ℹ️ 用户未登录，跳过订阅状态和应用配置查询');
        return;
      }
    }
  };

  // 免费版到Pro版的映射关系
  const getProVersionMapping = (appNameEn: string): string | null => {
    const mappings: Record<string, string> = {
      'novax-base': 'novax-pro',
      'elavax-base': 'elavax-pro',
      // 可以根据需要添加更多映射
    }
    return mappings[appNameEn] || null
  }

  // 获取所有app的订阅状态（优化版，考虑登录状态）
  useEffect(() => {
    // 只有在有应用列表时才获取订阅状态
    if (appList.length > 0) {
      console.log('📋 应用列表已加载，准备获取订阅状态', {
        appCount: appList.length,
        hasToken: !!Cookies.get('medxyToken')
      });
      fetchAllAppSubscriptions();
    }
  }, [xAiApi, appList]);

  // 监听登录状态变化，登录后重新获取应用列表和订阅状态（优化版，避免循环调用）
  useEffect(() => {
    const handleLoginSuccess = (event: CustomEvent) => {
      console.log('📢 Home: 收到登录成功事件，重新获取应用列表和订阅状态', {
        eventDetail: event.detail,
        timestamp: new Date().toISOString()
      });

      // 登录成功后需要重新获取应用列表和订阅状态
      // 稍作延迟确保token已完全设置
      setTimeout(async () => {
        try {
          console.log('🔄 Home: 登录后重新获取应用列表');
          // 强制刷新应用列表
          const appService = AppService.getInstance();
          const updatedAppList = await appService.getAppList(true); // 强制刷新
          setAppList(updatedAppList); // 直接调用setAppList触发重新渲染

          // 如果当前应用UUID为空，设置为第一个应用
          if (!currentAppUuid && updatedAppList.length > 0) {
            setCurrentAppUuid(updatedAppList[0].appUuid);
          }

          console.log('✅ Home: 登录后应用列表更新成功，准备获取订阅状态');
          // 重新获取订阅状态
          if (updatedAppList.length > 0) {
            await fetchAllAppSubscriptions(true); // 强制刷新订阅状态
          }
        } catch (error) {
          console.error('❌ Home: 登录后更新应用列表失败:', error);
        }
      }, 400);
    };

    console.log('🔧 Home: 注册loginSuccess事件监听器');
    // 只监听loginSuccess事件，移除userInfoUpdated监听以减少重复
    // userInfoUpdated事件会被其他地方处理
    window.addEventListener('loginSuccess', handleLoginSuccess as EventListener);

    return () => {
      console.log('🧹 Home: 移除loginSuccess事件监听器');
      window.removeEventListener('loginSuccess', handleLoginSuccess as EventListener);
    };
  }, [currentAppUuid, fetchAllAppSubscriptions]); // 添加必要的依赖

  // 监听medxyToken变化，检测弹框登录成功
  useCookieMonitor({
    cookieName: 'medxyToken',
    onCookieChange: (newValue, oldValue) => {
      // 检测从无token到有token的变化（弹框登录成功）
      if (!oldValue && newValue) {
        console.log('🎯 Home: 检测到弹框登录成功，medxyToken从无到有', {
          newToken: newValue,
          timestamp: new Date().toISOString()
        });

        // 执行与loginSuccess事件相同的处理逻辑
        setTimeout(async () => {
          try {
            console.log('🔄 Home: 弹框登录后重新获取应用列表');
            // 强制刷新应用列表
            const appService = AppService.getInstance();
            const updatedAppList = await appService.getAppList(true); // 强制刷新
            setAppList(updatedAppList); // 直接调用setAppList触发重新渲染

            // 如果当前应用UUID为空，设置为第一个应用
            if (!currentAppUuid && updatedAppList.length > 0) {
              setCurrentAppUuid(updatedAppList[0].appUuid);
            }

            console.log('✅ Home: 弹框登录后应用列表更新成功，准备获取订阅状态');
            // 重新获取订阅状态
            if (updatedAppList.length > 0) {
              await fetchAllAppSubscriptions(true); // 强制刷新订阅状态
            }
          } catch (error) {
            console.error('❌ Home: 弹框登录后更新应用列表失败:', error);
          }
        }, 400);
      }
    }
  });

  const onAppSelect = useCallback((appUuid: string) => {
    // 通过国际化路由导航切换应用，而不是直接设置状态
    const targetApp = appList.find(app => app.appUuid === appUuid || app.appNameEn === appUuid);
    if (targetApp) {
      // 添加详细的调试日志
      console.log('onAppSelect: 应用选择调试信息', {
        targetAppUuid: appUuid,
        targetAppName: targetApp.appNameEn,
        isRootAccess,
        rootAppName,
        currentPath: window.location.pathname,
        shouldAvoidNavigation: isRootAccess && rootAppName === targetApp.appNameEn
      });

      // 检查是否从根路径导航到相同应用，如果是则避免导航
      if (isRootAccess && rootAppName === targetApp.appNameEn) {
        console.log('避免不必要的导航：根路径已显示目标应用', {
          isRootAccess,
          currentApp: rootAppName,
          targetApp: targetApp.appNameEn,
          action: '跳过导航'
        });
        return;
      }

      console.log('切换应用到:', targetApp.appNameEn);
      navigateToApp(targetApp.appNameEn);
    }
  }, [appList, navigateToApp, isRootAccess, rootAppName]);

  // 监听showSubscriptionModal状态变化，处理免费订阅逻辑
  useEffect(() => {
    if (showSubscriptionModal && currentApp) {
      // 检查第一个套餐是否为免费
      const subStatusDetail = appListSub.get(currentApp.appNameEn)
      if (subStatusDetail?.subStatus === 1 || subStatusDetail?.subStatus === 3) {
        // 已订阅其他版本或退订
        // 检查是否已订阅免费版
        if (subStatusDetail?.packageType === '免费') {
          console.log('用户已订阅免费版，检查是否需要切换到Pro版')

          // 获取对应的Pro版应用名称
          const proAppName = getProVersionMapping(currentApp.appNameEn)
          if (proAppName) {
            onAppSelect(proAppName);
            // setShowSubscriptionModal(false)
          }
        }
        return;
      }


    }
  }, [showSubscriptionModal, currentApp, appListSub, onAppSelect]);

  // 如果指定的应用在当前语言环境不存在，显示404页面
  if (appName && effectiveAppUuid === null) {
    console.log('Home: 显示404页面，应用在当前语言环境不存在', { appName });
    return <NotFound />;
  }

  if (!currentApp) return null; // 组件加载失败时返回空或加载提示

  const handleMainClick = () => {
    if (isMobile() && sidebarOpen) {
      setSidebarOpen(false);
    }
  };

  return (
    <>
      {/* SEO 元数据 */}
      <SEOHead
        pageType="home"
        appKey={currentApp?.appNameEn}
        ogImage={currentApp?.appIcon}
      />

      <div className="flex h-screen" style={{ backgroundColor: 'var(--bg-main)' }}>
      {/* 移动端遮罩层 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 左侧边栏 - 响应式 */}
      <div className={`
        fixed md:relative inset-y-0 left-0 z-40 md:z-auto
        transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        transition-transform duration-300 ease-in-out
      `}>
        <Sidebar
          onSearchClick={() => setSearchModalOpen(true)}
          onClose={() => setSidebarOpen(false)}
          onConversationClick={() => setConversationModalOpen(true)}
          difyApi={difyApi}
          currentApp={currentApp}
          isCollapsed={sidebarCollapsed}
        />
      </div>

      {/* 主要内容区域 */}
      <main
        className={`flex-1 flex flex-col w-full md:w-auto ${
          isArtifactsResizing ? '' : 'transition-all duration-300'
        }`}
        style={{
          marginRight: artifactsDrawerOpen ? `${artifactsDrawerWidth}vw` : '0'
        }}
        onClick={handleMainClick}
      >
        {/* 顶部标题栏 - 固定定位 */}
        <Header
          onMenuClick={() => {
            const isDesktop = window.innerWidth >= 768;
            if (isDesktop) {
              // PC端：如果侧边栏已打开，则切换折叠状态；如果关闭，则打开
              if (sidebarOpen) {
                setSidebarCollapsed(!sidebarCollapsed);
              } else {
                setSidebarOpen(true);
                setSidebarCollapsed(false);
              }
            } else {
              // 移动端：直接切换显示/隐藏
              setSidebarOpen(!sidebarOpen);
            }
          }}
          sidebarOpen={sidebarOpen}
          sidebarCollapsed={sidebarCollapsed}
          currentApp={currentApp}
          onAppSelect={onAppSelect}
          appList={appList}
          xAiApi={xAiApi}
          subStatusDetail={appListSub.get(currentApp.appNameEn)!}
          showSubscriptionModal={showSubscriptionModal}
          setShowSubscriptionModal={setShowSubscriptionModal}
          appListSub={appListSub}
          fetchAllAppSubscriptions={fetchAllAppSubscriptions}
          setAppList={setAppList}
          // Add artifacts drawer props
          artifactsDrawerOpen={artifactsDrawerOpen}
          onArtifactsToggle={() => setArtifactsDrawerOpen(!artifactsDrawerOpen)}
          showArtifactsButton={currentApp.appNameEn === 'datascore-base'}
        />

        {/* 主要内容 - 添加顶部间距避免被固定头部遮挡，允许滚动 */}
        <div className="pt-16 md:pt-20 flex-1 overflow-y-auto">
          <MainContent
            onAppSelect={onAppSelect}
            difyApi={difyApi}
            xAiApi={xAiApi}
            currentApp={currentApp}
            appList={appList}
            appParam={appParam}
            appListSub={appListSub}
            preCheck={preCheck}
            onStartCompetition={handleStartCompetition}
            onShowSurvey={handleShowSurvey}
          />
        </div>
      </main>



      {/* 搜索模态框 */}
      {searchModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">搜索</h3>
              <button
                onClick={() => setSearchModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <input
              type="text"
              placeholder="搜索..."
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              autoFocus
            />
          </div>
        </div>
      )}

      {/* 会话历史模态框 */}
      {conversationModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setConversationModalOpen(false)}
        >
          <div
            className="bg-white rounded-2xl w-full max-w-4xl h-[80vh] flex flex-col shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            <ConversationsPage
              difyApi={difyApi}
              currentApp={currentApp}
              onClose={() => setConversationModalOpen(false)}
            />
          </div>
        </div>
      )}



      {/* 订阅弹框 - 根据设备类型显示不同组件 */}
      {currentApp && appListSub.get(currentApp.appNameEn) && !autoSubscribing && (
        isMobile() ? (
          <PayMobile
            isOpen={showSubscriptionModal}
            xAiApi={xAiApi}
            currentApp={currentApp}
            subStatusDetail={appListSub.get(currentApp.appNameEn)!}
            appUser={currentApp.appUser}
            onClose={() => setShowSubscriptionModal(false)}
            subscribe={subscribe}
            paymentLoading={paymentLoading}
          />
        ) : (
          <Pay
            isOpen={showSubscriptionModal}
            xAiApi={xAiApi}
            currentApp={currentApp}
            subStatusDetail={appListSub.get(currentApp.appNameEn)!}
            appUser={currentApp.appUser}
            onClose={() => {
                clearPaymentTimer()
                setShowSubscriptionModal(false)
            }}
            subscribe={subscribe}
            paymentLoading={paymentLoading}
            payUrl={payUrl}
            clearPaymentTimer={clearPaymentTimer}
          />
        )
      )}

      {/* Artifacts Drawer - Only for datascore-base */}
      {currentApp.appNameEn === 'datascore-base' && (
        <ArtifactsDrawer
          isOpen={artifactsDrawerOpen}
          onClose={() => setArtifactsDrawerOpen(false)}
          width={artifactsDrawerWidth}
          onWidthChange={setArtifactsDrawerWidth}
          onResizeStart={() => setIsArtifactsResizing(true)}
          onResizeEnd={() => setIsArtifactsResizing(false)}
        />
      )}
      </div>
    </>
  );
};

export default Home;
