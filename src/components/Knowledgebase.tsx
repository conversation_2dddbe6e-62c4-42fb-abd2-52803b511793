import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import PageTitle from './PageTitle';
import type { DifyApi, IGetAppParametersResponse } from '../api/src/dify-api';
import type { XAiApi, IGetAiAppInfoResponse, PackageByKey } from '../api/src/xai-api';
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks';
import { isMobile } from '../api/src/utils';
import Cookies from 'js-cookie';
import { message } from 'antd';
import KnowledgebaseContent from './knowledgebase/KnowledgebaseContent';
import { getDomainForConfigKey } from '../utils/domainUtils';
import { AppService } from '../utils/appService';

interface KnowledgebaseProps {
  difyApi: DifyApi
  xAiApi: XAiApi
  user: string
}

const Knowledgebase: React.FC<KnowledgebaseProps> = ({ difyApi, xAiApi, user }) => {
  const { t } = useSimpleTranslation()
  const { navigateToApp, currentLanguage } = useI18nRouter()

  // 应用列表状态
  const [appList, setAppList] = useState<IGetAiAppInfoResponse[]>([])
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    // PC端默认显示侧边栏，移动端默认隐藏
    return window.innerWidth >= 768; // md断点是768px
  });
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [conversationModalOpen, setConversationModalOpen] = useState(false);
  const [appParam, setAppParam] = useState<IGetAppParametersResponse>()
  const [appListSub, setAppListSub] = useState<Map<string, PackageByKey | null>>(new Map())

  const { appName } = useParams();
  const navigate = useNavigate();

  // 获取应用列表（使用AppService避免重复调用）
  useEffect(() => {
    const lang = currentLanguage || 'zh';
    Cookies.set("ai_apps_lang", lang, {domain: '.medsci.cn'});
    Cookies.set("ai_apps_lang", lang, {domain: '.medon.com.cn'});

    const fetchAppList = async () => {
      try {
        console.log('🔍 Knowledgebase: 使用AppService获取应用列表，避免重复API调用', {
          currentLanguage: lang,
          pathname: window.location.pathname,
          timestamp: new Date().toISOString()
        });

        const appService = AppService.getInstance();
        const apps = await appService.getAppList();

        console.log('📊 Knowledgebase: AppService返回应用列表', {
          appsLength: apps.length,
          apps: apps.map(app => ({ appNameEn: app.appNameEn, appName: app.appName }))
        });

        setAppList(apps);
      } catch (error) {
        console.error('🔍 Knowledgebase: 获取应用列表失败:', error);
      }
    };

    fetchAppList();
  }, [currentLanguage])

  // 更新API配置
  useEffect(() => {
    const medxyToken = Cookies.get('medxyToken')
    if (user && medxyToken) {
      console.log('Updated user====', user)
      xAiApi.updateOptions({
        user: user,
        apiBase: xAiApi.options.apiBase,
        medxyToken: medxyToken
      });
    }
  }, [user, xAiApi]) // 依赖 user

  // ESC键关闭弹窗功能
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // 按优先级关闭弹窗：会话历史 > 搜索 > 侧边栏
        if (conversationModalOpen) {
          setConversationModalOpen(false)
        } else if (searchModalOpen) {
          setSearchModalOpen(false)
        } else if (sidebarOpen) {
          const isDesktop = window.innerWidth >= 768;
          if (isDesktop) {
            // PC端：折叠侧边栏，效果和点击菜单图标一样
            setSidebarCollapsed(true);
          } else {
            // 移动端：完全关闭侧边栏
            setSidebarOpen(false);
          }
        }
      }
    }

    // 只在有弹窗打开时添加监听器
    if (conversationModalOpen || searchModalOpen || sidebarOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => {
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [conversationModalOpen, searchModalOpen, sidebarOpen])

  // 监听窗口大小变化，调整侧边栏状态
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 768
      // 如果从移动端切换到PC端，自动显示侧边栏并重置折叠状态
      if (isDesktop && !sidebarOpen) {
        setSidebarOpen(true)
        setSidebarCollapsed(false)
      }
      // 如果从PC端切换到移动端，自动隐藏侧边栏并重置折叠状态
      else if (!isDesktop && sidebarOpen) {
        setSidebarOpen(false)
        setSidebarCollapsed(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [sidebarOpen])

  // 使用第一个应用作为当前应用，如果没有应用则创建一个默认的知识库应用
  const currentApp = useMemo(() => {
    if (appList.length > 0) {
      return appList[0];
    }
    // 为知识库创建一个完整的默认应用对象，避免页面空白
    return {
      id: 0,
      appUuid: 'knowledgebase-default',
      appNameEn: 'knowledgebase',
      appName: currentLanguage === 'en' ? 'Knowledge Base' : '知识库',
      appLang: currentLanguage === 'en' ? '英文' : '中文',
      appStatus: 'active',
      appIcon: '',
      appDescription: currentLanguage === 'en' ? 'Data Storage and Management' : '数据存储和管理',
      appType: 'knowledgebase',
      appUser: {
        id: 0,
        socialUserId: 0,
        socialType: 0,
        appUuid: 'knowledgebase-default',
        status: 'active',
        useNum: '0',
        expireAt: '2099-12-31'
      },
      feeTypes: [],
      isInternalUser: 0,
      dAppUuid: 'knowledgebase-default',
      directoryMd: ''
    };
  }, [appList, currentLanguage])

  const onAppSelect = useCallback((appUuid: string) => {
    // 通过国际化路由导航切换应用，而不是直接设置状态
    const targetApp = appList.find(app => app.appUuid === appUuid || app.appNameEn === appUuid);
    if (targetApp) {
      console.log('切换应用到:', targetApp.appNameEn);
      navigateToApp(targetApp.appNameEn);
    }
  }, [appList, navigateToApp]);

  console.log('🎯 Knowledgebase: 当前应用状态', {
    currentApp,
    appListLength: appList.length,
    isDefaultApp: currentApp?.appUuid === 'knowledgebase-default'
  });

  const handleMainClick = () => {
    if (isMobile() && sidebarOpen) {
      setSidebarOpen(false);
    }
  };

  return (
    <>
      {/* 页面标题 */}
      <PageTitle
        pageType="knowledgebase"
        appKey="knowledgebase"
      />

      <div className="flex h-screen" style={{ backgroundColor: 'var(--bg-main)' }}>
      {/* 移动端遮罩层 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 左侧边栏 - 响应式 */}
      <div className={`
        fixed md:relative inset-y-0 left-0 z-40 md:z-auto
        transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        transition-transform duration-300 ease-in-out
      `}>
        <Sidebar
          onSearchClick={() => setSearchModalOpen(true)}
          onClose={() => setSidebarOpen(false)}
          onConversationClick={() => setConversationModalOpen(true)}
          difyApi={difyApi}
          currentApp={currentApp}
          isCollapsed={sidebarCollapsed}
        />
      </div>

      {/* 主要内容区域 */}
      <main className="flex-1 flex flex-col w-full md:w-auto" onClick={handleMainClick}>
        {/* 顶部标题栏 - 固定定位 */}
        <Header
          onMenuClick={() => {
            const isDesktop = window.innerWidth >= 768;
            if (isDesktop) {
              // PC端：如果侧边栏已打开，则切换折叠状态；如果关闭，则打开
              if (sidebarOpen) {
                setSidebarCollapsed(!sidebarCollapsed);
              } else {
                setSidebarOpen(true);
                setSidebarCollapsed(false);
              }
            } else {
              // 移动端：直接切换显示/隐藏
              setSidebarOpen(!sidebarOpen);
            }
          }}
          sidebarOpen={sidebarOpen}
          sidebarCollapsed={sidebarCollapsed}
          currentApp={currentApp}
          onAppSelect={onAppSelect}
          appList={appList}
          xAiApi={xAiApi}
          subStatusDetail={appListSub.get(currentApp.appNameEn)!}
          showSubscriptionModal={false}
          setShowSubscriptionModal={() => {}}
          appListSub={appListSub}
          fetchAllAppSubscriptions={async () => {}}
          setAppList={setAppList}
        />

        {/* 主要内容 - 添加顶部间距避免被固定头部遮挡，允许滚动 */}
        <div className="pt-16 md:pt-20 flex-1 overflow-y-auto">
          {/* 知识库标题区域 */}
          <div className="p-6 md:p-8">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-8">
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-2">
                  {t('knowledgebase.title')}
                </h1>
                <p className="text-base md:text-lg text-gray-600">
                  {t('knowledgebase.subtitle')}
                </p>
              </div>
            </div>
          </div>

          {/* 知识库内容区域 */}
          <div className="px-6 md:px-8 pb-8">
            <div className="max-w-6xl mx-auto">
              <KnowledgebaseContent user={user} xAiApi={xAiApi} />
            </div>
          </div>
        </div>
      </main>

      {/* 搜索模态框 */}
      {searchModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">{t('common.search')}</h3>
              <button
                onClick={() => setSearchModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <input
              type="text"
              placeholder={`${t('common.search')}...`}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              autoFocus
            />
          </div>
        </div>
      )}
      </div>
    </>
  );
};

export default Knowledgebase;