import type React from 'react'
import { useState, useRef, useEffect } from 'react'
import { useI18nRouter } from '../i18n/simple-hooks'
import { LANGUAGE_CONFIG, SUPPORTED_LANGUAGES, type SupportedLanguage, setLanguage } from '../i18n/simple'

interface LanguageSwitcherProps {
  className?: string
  showFlag?: boolean
  showText?: boolean
  dropdownPosition?: 'left' | 'right'
  size?: 'small' | 'medium' | 'large'
}

/**
 * 语言切换器组件
 * 支持中英文切换，保持用户偏好设置
 */
const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  className = '',
  showFlag = true,
  showText = true,
  dropdownPosition = 'right',
  size = 'medium'
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const { currentLanguage, changeLanguage } = useI18nRouter()

  // 简单的翻译函数
  const t = (key: string): string => {
    const translations = {
      'header.switchLanguage': currentLanguage === 'zh' ? '切换语言' : 'Switch Language'
    }
    return translations[key as keyof typeof translations] || key
  }

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // 处理语言切换
  const handleLanguageChange = (lang: SupportedLanguage) => {
    if (lang !== currentLanguage) {
      // 保存语言设置到全局状态
      setLanguage(lang)
      // 切换语言和路由
      changeLanguage(lang)
    }
    setIsOpen(false)
  }

  // 获取当前语言配置
  const currentLangConfig = LANGUAGE_CONFIG[currentLanguage]

  // 尺寸样式
  const sizeClasses = {
    small: 'text-sm px-2 py-1',
    medium: 'text-base px-3 py-2',
    large: 'text-lg px-4 py-3'
  }

  // 下拉菜单位置样式 - 优化定位
  const dropdownPositionClasses = {
    left: 'right-0',      // 右对齐，向左展开
    right: 'left-0'       // 左对齐，向右展开
  }

  return (
    <div className={`relative inline-block ${className}`} ref={dropdownRef}>
      {/* 触发按钮 - 优化设计 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          flex items-center space-x-2
          h-[40px] px-3
          bg-white dark:bg-gray-800
          border border-gray-200 dark:border-gray-600
          rounded-xl shadow-sm
          hover:bg-gray-50 dark:hover:bg-gray-700
          hover:border-gray-300 dark:hover:border-gray-500
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          transition-all duration-200
          group
        `}
        aria-label={t('header.switchLanguage')}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {/* 语言标志 */}
        {showFlag && (
          <span className="text-base" role="img" aria-label={currentLangConfig.name}>
            {currentLangConfig.flag}
          </span>
        )}

        {/* 语言文本 - 只显示语言名称，不显示前缀 */}
        {showText && (
          <span className="font-medium text-sm text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-200">
            {currentLangConfig.name}
          </span>
        )}

        {/* 下拉箭头 - 优化样式 */}
        <svg
          className={`w-3.5 h-3.5 text-gray-400 group-hover:text-gray-600 transition-all duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div
          className={`
            absolute top-full mt-2
            ${dropdownPositionClasses[dropdownPosition]}
            bg-white dark:bg-gray-800
            border border-gray-100 dark:border-gray-600
            rounded-xl shadow-xl
            z-50 min-w-[110px] sm:min-w-[120px]
            overflow-hidden
            backdrop-blur-sm
            fade-in
          `}
          role="menu"
          aria-orientation="vertical"
        >
          {SUPPORTED_LANGUAGES.map((lang) => {
            const langConfig = LANGUAGE_CONFIG[lang]
            const isSelected = lang === currentLanguage
            
            return (
              <button
                key={lang}
                onClick={() => handleLanguageChange(lang)}
                className={`
                  w-full flex items-center space-x-2 px-3 py-2.5
                  text-left hover:bg-gray-50 dark:hover:bg-gray-700
                  transition-all duration-200
                  group
                  ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-200'}
                  ${lang === SUPPORTED_LANGUAGES[0] ? 'rounded-t-xl' : ''}
                  ${lang === SUPPORTED_LANGUAGES[SUPPORTED_LANGUAGES.length - 1] ? 'rounded-b-xl' : ''}
                `}
                role="menuitem"
                aria-selected={isSelected}
              >
                {/* 语言标志 */}
                <span className="text-base group-hover:scale-110 transition-transform duration-200" role="img" aria-label={langConfig.name}>
                  {langConfig.flag}
                </span>

                {/* 语言名称 - 简化显示 */}
                <span className="font-medium flex-1 text-sm">
                  {langConfig.name}
                </span>

                {/* 选中标记 - 确保可见性 */}
                {isSelected && (
                  <div className="flex items-center justify-center w-4 h-4 flex-shrink-0">
                    <svg
                      className="w-4 h-4 text-blue-600 dark:text-blue-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </button>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default LanguageSwitcher

/**
 * 简化版语言切换器（仅图标）- 优化设计
 */
export const LanguageSwitcherIcon: React.FC<{
  className?: string
  onClick?: () => void
}> = ({ className = '', onClick }) => {
  const { currentLanguage, changeLanguage } = useI18nRouter()

  // 简单的翻译函数
  const t = (key: string): string => {
    const translations = {
      'header.switchLanguage': currentLanguage === 'zh' ? '切换语言' : 'Switch Language'
    }
    return translations[key as keyof typeof translations] || key
  }

  const handleClick = () => {
    // 切换到另一种语言
    const nextLang = currentLanguage === 'zh' ? 'en' : 'zh'
    // 保存语言设置到全局状态
    setLanguage(nextLang)
    // 切换语言和路由
    changeLanguage(nextLang)
    onClick?.()
  }

  const currentLangConfig = LANGUAGE_CONFIG[currentLanguage]

  return (
    <button
      onClick={handleClick}
      className={`
        p-2 rounded-xl
        bg-white dark:bg-gray-800
        border border-gray-200 dark:border-gray-600
        hover:bg-gray-50 dark:hover:bg-gray-700
        hover:border-gray-300 dark:hover:border-gray-500
        focus:outline-none focus:ring-2 focus:ring-blue-500
        transition-all duration-200
        hover:scale-105
        ${className}
      `}
      aria-label={t('header.switchLanguage')}
      title={t('header.switchLanguage')}
    >
      <span className="text-base" role="img" aria-label={currentLangConfig.name}>
        {currentLangConfig.flag}
      </span>
    </button>
  )
}
