import type React from 'react'
import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from 'antd'
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons'
import { getCurrentLanguage, t } from '../i18n/simple'
import { AppService } from '../utils/appService'

/**
 * 404错误页面组件 - 支持多语言和智能应用跳转
 */
const NotFound: React.FC = () => {
  const navigate = useNavigate()
  const [isLoading, setIsLoading] = useState(false)
  const [firstAppName, setFirstAppName] = useState<string>('')

  // 获取当前语言和第一个可用应用
  useEffect(() => {
    const initializeAppInfo = async () => {
      try {
        const appService = AppService.getInstance()

        // 尝试从缓存获取第一个应用
        const cacheStatus = appService.getCacheStatus()
        let appName = ''

        if (cacheStatus.isValid) {
          // 使用缓存数据
          appName = appService.getFirstAvailableAppNameSync()
          console.log('NotFound: 使用缓存获取第一个应用', {
            appName,
            cacheAge: `${(cacheStatus.age / 1000).toFixed(1)}秒`
          })
        } else {
          // 缓存无效，异步获取
          console.log('NotFound: 缓存无效，异步获取第一个应用')
          appName = await appService.getFirstAvailableAppName()
          console.log('NotFound: 异步获取第一个应用成功', { appName })
        }

        setFirstAppName(appName)
      } catch (error) {
        console.error('NotFound: 获取第一个应用失败', error)
        // 根据环境决定fallback策略
        const isInternationalMode = import.meta.env.MODE === 'international'
        if (isInternationalMode) {
          // 在国际站环境下，如果当前语言没有应用，不设置fallback应用名
          setFirstAppName('')
        } else {
          // 在非国际站环境下，使用novax-base作为fallback
          setFirstAppName('novax-base')
        }
      }
    }

    initializeAppInfo()
  }, [])

  // 返回首页 - 跳转到第一个可用应用
  const handleGoHome = async () => {
    setIsLoading(true)

    try {
      const currentLanguage = getCurrentLanguage()
      const isInternationalMode = import.meta.env.MODE === 'international'
      let targetAppName = firstAppName

      // 如果还没有获取到第一个应用，尝试实时获取
      if (!targetAppName) {
        console.log('NotFound: 实时获取第一个应用')
        const appService = AppService.getInstance()
        try {
          targetAppName = await appService.getFirstAvailableAppName()
        } catch (error) {
          console.log('NotFound: 当前语言没有可用应用', { currentLanguage, error })

          // 在国际站环境下，如果当前语言没有应用，尝试切换到英文
          if (isInternationalMode && currentLanguage === 'zh') {
            console.log('NotFound: 国际站中文无应用，重定向到英文版')
            navigate('/en')
            return
          }

          throw error
        }
      }

      const targetPath = `/${currentLanguage}/${targetAppName}`

      console.log('NotFound: 跳转到第一个可用应用', {
        currentLanguage,
        targetAppName,
        targetPath,
        isInternationalMode
      })

      navigate(targetPath)
    } catch (error) {
      console.error('NotFound: 跳转失败，使用fallback', error)
      // fallback到根路径
      navigate('/')
    } finally {
      setIsLoading(false)
    }
  }

  // 返回上一页
  const handleGoBack = () => {
    const isInternationalMode = import.meta.env.MODE === 'international'

    // 在国际站环境下，不使用浏览器历史记录，直接跳转到首页
    // 因为历史记录中可能包含无效的URL（如novax-base）
    if (isInternationalMode) {
      console.log('NotFound: 国际站环境，返回上一页改为跳转首页')
      handleGoHome()
      return
    }

    // 在非国际站环境下，使用原有逻辑
    if (window.history.length > 1) {
      navigate(-1)
    } else {
      handleGoHome()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* 404图标 */}
        <div className="mb-8">
          <div className="text-8xl font-bold text-blue-500 mb-4">404</div>
          <div className="w-24 h-1 bg-blue-500 mx-auto rounded-full"></div>
        </div>

        {/* 错误信息 */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {t('error.pageNotFound')}
          </h1>
          <p className="text-gray-600 leading-relaxed">
            {t('error.pageNotFoundDescription')}
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="space-y-4">
          <Button
            type="primary"
            size="large"
            icon={<HomeOutlined />}
            onClick={handleGoHome}
            loading={isLoading}
            className="w-full h-12 text-base font-medium"
          >
            {t('common.goToHomepage')}
          </Button>

          <Button
            size="large"
            icon={<ArrowLeftOutlined />}
            onClick={handleGoBack}
            className="w-full h-12 text-base font-medium"
          >
            {t('common.goBack')}
          </Button>
        </div>

        {/* 装饰性元素 */}
        <div className="mt-12 opacity-50">
          <div className="flex justify-center space-x-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NotFound
