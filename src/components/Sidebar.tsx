import { useNavigate, useParams, useLocation } from 'react-router-dom'
import type React from 'react'
import { useState, useEffect, useCallback, useRef } from 'react'
import './icons/styles.css'
import { DifyApi, type IConversationItem } from '../api/src/dify-api'
import { XAiApi, type IGetAiAppInfoResponse } from '../api/src/xai-api'
import Cookies from 'js-cookie'
import { useI18nRouter, useSimpleTranslation } from '../i18n/simple-hooks'
import { useLoginStatus } from '../hooks/useLoginStatus'
import { HistoryIcon, InfoIcon, UsersIcon } from './icons/Icons'
import AboutUsModal from './about/AboutUsModal'
import FaqModal from './faq/FaqModal'
import FeedbackModal from './FeedbackModal'
import { message } from 'antd'
import { isMobile } from '../api/src/utils'
import { ConversationRefreshManager } from '../utils/conversationRefreshManager'
import { ApiCallDeduplicator } from '../utils/apiCallDeduplicator'

interface ApiProps {
  conversationList: IConversationItem[]
  onConversationClick: () => void
  appUuid: string
  onClose?: () => void
  difyApi: DifyApi
  onConversationUpdate: (updatedList: IConversationItem[]) => void
}

// 简化的历史提问列表组件（适用于侧边栏）
const SidebarConversationList: React.FC<ApiProps> = ({
  conversationList,
  onConversationClick,
  appUuid,
  onClose,
  difyApi,
  onConversationUpdate
}) => {
  const navigate = useNavigate()
  const { navigateToApp } = useI18nRouter()
  const { t } = useSimpleTranslation()

  // 重命名相关状态
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState<string>('');
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  const handleConversationClick = (conversationId: string) => {
    // 如果正在编辑，不响应点击
    if (editingId === conversationId) return;

    // 在移动端点击后关闭侧边栏
    if (isMobile() && onClose) {
      onClose()
    }
    // 使用新的国际化路由结构
    if (appUuid) {
      navigateToApp(appUuid, conversationId)
    }
  }

  // 处理重命名点击
  const handleRenameClick = (e: React.MouseEvent, item: IConversationItem) => {
    e.stopPropagation();
    setEditingId(item.id);
    setEditingName(item.name);
  };

  // 处理保存重命名
  const handleSaveRename = async (e: React.MouseEvent, conversationId: string) => {
    e.stopPropagation();
    if (!editingName.trim()) return;

    try {
      // 调用API重命名对话
      await difyApi.renameConversation({
        conversation_id: conversationId,
        name: editingName.trim(),
        appId: difyApi.options.appId || ''
      });

      // 更新本地状态
      const updatedList = conversationList.map(item =>
        item.id === conversationId
          ? { ...item, name: editingName.trim() }
          : item
      );
      onConversationUpdate(updatedList);

      // 显示成功提示
      // message.success(t('history.renameSuccess'));

      // 退出编辑状态
      setEditingId(null);
      setEditingName('');

      // 清除所有对话列表相关的API缓存并触发其他组件刷新
      console.log('🔄 SidebarConversationList: 重命名成功，清除所有对话列表缓存并触发全局刷新');
      ApiCallDeduplicator.clearAllConversationCache();
      ConversationRefreshManager.triggerRefreshDelayed(100);
    } catch (error) {
      console.error('重命名对话失败:', error);
      message.error(t('history.renameError'));
    }
  };

  // 处理取消编辑
  const handleCancelEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingId(null);
    setEditingName('');
  };

  // 限制显示最多10个对话，取最新的10个
  const displayedConversations = conversationList.slice(0, 10);
  const hasMoreConversations = conversationList.length > 10;

  return (
    <div className="relative">
      <ul className="relative pl-0 list-none m-0">
        {displayedConversations.length > 0 ? (
          displayedConversations.map((conversation, index) => {
            const isLast = index === displayedConversations.length - 1;
            const isEditing = editingId === conversation.id;
            const isHovered = hoveredId === conversation.id;

            return (
              <li
                key={conversation.id}
                onClick={() => handleConversationClick(conversation.id)}
                onMouseEnter={() => setHoveredId(conversation.id)}
                onMouseLeave={() => setHoveredId(null)}
                className={`group relative py-1 text-sm rounded-md mx-1 px-2 transition-all duration-200 ${
                  isEditing
                    ? 'cursor-default'
                    : 'cursor-pointer text-gray-500 hover:text-blue-600'
                }`}
              >
                {/* 垂直连接线 - 对于最后一个项目，只显示到中间位置 */}
                {!isLast && (
                  <div
                    className="absolute left-[8px] top-[50%] bottom-0"
                    style={{
                      borderLeft: '1px solid #d1d5db'
                    }}
                  ></div>
                )}
                {/* 从顶部到中间的连接线 - 最后项目且有圆角时不显示 */}
                {!isLast && (
                  <div
                    className="absolute left-[8px] top-0 h-[50%]"
                    style={{
                      borderLeft: '1px solid #d1d5db'
                    }}
                  ></div>
                )}

                {/* 对于最后一个项目，添加圆角连接 */}
                {isLast ? (
                  <div
                    className="absolute left-[8px] top-0"
                    style={{
                      width: '12px',
                      height: '50%',
                      borderLeft: '1px solid #d1d5db',
                      borderBottom: '1px solid #d1d5db',
                      borderBottomLeftRadius: '6px',
                    }}
                  ></div>
                ) : (
                  /* 水平连接线 - 只对非最后项目显示 */
                  <div
                    className="absolute left-[8px] top-[50%] w-3 transform -translate-y-1/2"
                    style={{
                      borderBottom: '1px solid #d1d5db'
                    }}
                  ></div>
                )}

                <div className={`flex items-center w-full ${isEditing ? 'pl-5' : 'pl-6'}`}>
                  {isEditing ? (
                    // 编辑状态 - 固定布局
                    <div className="flex items-center w-full relative">
                      <div className="flex-1 pr-16">
                        <input
                          type="text"
                          value={editingName}
                          onChange={(e) => setEditingName(e.target.value)}
                          onClick={(e) => e.stopPropagation()}
                          className="w-full text-sm px-1 py-0 border-none outline-none bg-transparent text-gray-900 font-medium truncate"
                          autoFocus
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleSaveRename(e as any, conversation.id);
                            } else if (e.key === 'Escape') {
                              handleCancelEdit(e as any);
                            }
                          }}
                          onBlur={(e) => {
                            // 延迟执行，让按钮点击事件先执行
                            setTimeout(() => {
                              if (editingId === conversation.id) {
                                handleCancelEdit(e as any);
                              }
                            }, 150);
                          }}
                        />
                      </div>
                      <div className="absolute right-0 flex gap-1 flex-shrink-0">
                        <button
                          onClick={handleCancelEdit}
                          className="p-1 md:p-0.5 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors flex-shrink-0 min-w-[24px] min-h-[24px] md:min-w-auto md:min-h-auto flex items-center justify-center"
                          title={t('history.cancel')}
                        >
                          <svg className="w-4 h-4 md:w-3.5 md:h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                        <button
                          onClick={(e) => handleSaveRename(e, conversation.id)}
                          className="p-1 md:p-0.5 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors flex-shrink-0 min-w-[24px] min-h-[24px] md:min-w-auto md:min-h-auto flex items-center justify-center"
                          title={t('history.save')}
                        >
                          <svg className="w-4 h-4 md:w-3.5 md:h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  ) : (
                    // 正常显示状态
                    <>
                      <span
                        className="tree-item-text block truncate group-hover:font-medium transition-all duration-200 flex-1"
                        title={conversation.name}
                      >
                        {conversation.name}
                      </span>
                      {(isHovered || hoveredId === conversation.id) && (
                        <button
                          onClick={(e) => handleRenameClick(e, conversation)}
                          className="absolute right-0 p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-100 rounded transition-all duration-200 opacity-0 group-hover:opacity-100"
                          title={t('history.rename')}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          </svg>
                        </button>
                      )}
                    </>
                  )}
                </div>
              </li>
            );
          })

        ) : (
          <li className="relative py-2 text-sm text-gray-500 pl-6">
            {t('sidebar.noHistory')}
          </li>
        )}
        {conversationList.length > 0 && (
          <li
            onClick={() => onConversationClick()}
            className="relative py-2 text-sm text-gray-500 pl-6 cursor-pointer hover:text-blue-600 rounded-lg mx-2 transition-all duration-200 group"
          >
            <div className="flex items-center">
              <HistoryIcon
                width={14}
                height={14}
                className="mr-2 group-hover:text-blue-600 transition-colors duration-200"
                aria-label="查看历史记录"
              />
              <span className="group-hover:underline">
                {t('sidebar.viewAllHistory')}
              </span>
            </div>
          </li>
        )}
      </ul>
    </div>
  )
}

interface SidebarProps {
  onSearchClick: () => void
  onClose?: () => void
  onConversationClick: () => void
  difyApi: DifyApi
  currentApp: IGetAiAppInfoResponse
  isCollapsed?: boolean
}

const Sidebar = ({ onSearchClick, onClose, onConversationClick, difyApi, currentApp, isCollapsed = false }: SidebarProps) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { t } = useSimpleTranslation()
  const { navigateToApp } = useI18nRouter()
  const { appName } = useParams<{ appName?: string }>()
  const { isLoggedIn } = useLoginStatus()

  // 检测当前域名
  const currentDomain = window.location.hostname

  // 不显示ICP备案信息的域名列表
  const excludedDomains = ['medxy.ai', 'www.medxy.ai']
  const [appConversations, setAppConversations] = useState<Record<string, IConversationItem[]>>({})

  // 用于防抖的ref
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  // 用于跟踪最后一次API调用时间的ref
  const lastFetchTimeRef = useRef<Record<string, number>>({})
  const [showAboutModal, setShowAboutModal] = useState(false)
  const [showFaqModal, setShowFaqModal] = useState(false)
  const [showFeedbackModal, setShowFeedbackModal] = useState(false)

  // 获取指定app的conversation列表（智能缓存机制）
  const fetchAppConversations = useCallback(async (dAppUuid: string, forceRefresh = false) => {
    // 如果是默认的知识库应用，跳过对话历史加载
    if (dAppUuid === 'knowledgebase-default') {
      console.log('⏭️ Sidebar: 跳过默认知识库应用的对话历史加载', { dAppUuid })
      setAppConversations(prev => ({
        ...prev,
        [dAppUuid]: []
      }))
      return
    }

    const medxyToken = Cookies.get('medxyToken')
    if (!medxyToken) {
      console.log('🔒 Sidebar: 没有token，跳过历史记录加载')
      return
    }

    // 检查是否正在加载中
    const loadingKey = `loading_${dAppUuid}`
    if ((window as any)[loadingKey]) {
      console.log('⏰ Sidebar: 该应用的历史记录正在加载中，跳过重复请求')
      return
    }

    // 智能缓存检查
    const now = Date.now()
    const lastFetchTime = lastFetchTimeRef.current[dAppUuid] || 0
    const cacheTimeout = 30000 // 30秒缓存超时
    const hasValidCache = appConversations[dAppUuid] && (now - lastFetchTime) < cacheTimeout

    // 如果不是强制刷新且有有效缓存，则跳过
    if (!forceRefresh && hasValidCache) {
      console.log('📋 Sidebar: 使用缓存的历史记录', {
        dAppUuid,
        cacheAge: now - lastFetchTime,
        cacheTimeout
      })
      return
    }

    try {
      // 设置加载标志
      (window as any)[loadingKey] = true
      console.log('🔄 Sidebar: 开始加载历史记录', { dAppUuid, forceRefresh })

      // 创建临时的difyApi实例来获取该app的conversations
      // 使用最新的token确保认证正确
      const tempDifyApi = new DifyApi({
        user: difyApi.options.user,
        apiBase: difyApi.options.apiBase,
        medxyToken: medxyToken, // 使用最新的token
        appId: dAppUuid,
      })

      // 使用API去重工具防止重复调用
      const response = await ApiCallDeduplicator.getConversationListDeduped(
        dAppUuid,
        () => tempDifyApi.getConversationList(),
        forceRefresh
      );
      console.log('✅ Sidebar: 历史记录加载成功', response.data.length)

      // 更新时间戳
      lastFetchTimeRef.current[dAppUuid] = now

      setAppConversations(prev => ({
        ...prev, // 保留其他app的数据
        [dAppUuid]: response.data
      }))
    } catch (error) {
      // 如果是API冷却错误，不显示错误，静默等待
      if ((error as Error)?.message === 'API call blocked by cooldown period') {
        console.log('⏰ Sidebar: API调用被冷却机制阻止，将在后台重试')
        // 不设置空数组，保持现有数据
      } else {
        console.error('❌ Sidebar: 历史记录加载失败', error)
        setAppConversations(prev => ({
          ...prev,
          [dAppUuid]: []
        }))
      }
    } finally {
      // 清除加载标志
      delete (window as any)[loadingKey]
    }
  }, [difyApi, appConversations])

  // 统一的历史对话加载逻辑 - 覆盖所有场景
  useEffect(() => {
    // 清除之前的延迟调用
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current)
    }

    // 基本条件检查
    if (!currentApp?.dAppUuid || !isLoggedIn) {
      return
    }

    // 解析当前路径，确保是当前应用的页面
    const segments = location.pathname.split('/').filter(Boolean)
    const [lang, appName, sessionId] = segments

    // 只在当前应用的页面才加载历史对话
    if (appName !== currentApp.appNameEn) {
      console.log('🚫 Sidebar: 不是当前应用的页面，跳过历史记录加载', {
        currentPath: location.pathname,
        expectedApp: currentApp.appNameEn,
        actualApp: appName
      })
      return
    }

    // 使用防抖机制避免频繁调用
    refreshTimeoutRef.current = setTimeout(() => {
      console.log('🔄 Sidebar: 触发历史记录加载', {
        dAppUuid: currentApp.dAppUuid,
        appName: currentApp.appNameEn,
        currentPath: location.pathname,
        sessionId
      })

      // 调用API获取历史对话列表
      fetchAppConversations(currentApp.dAppUuid)
      refreshTimeoutRef.current = null
    }, 100) // 100ms防抖延迟

  }, [currentApp, isLoggedIn, location.pathname, fetchAppConversations]) // 依赖应用、登录状态和路径

  // 监听页面可见性变化，页面重新获得焦点时刷新历史对话
  useEffect(() => {
    const handleVisibilityChange = () => {
      // 只在页面重新可见且满足条件时刷新
      if (!document.hidden && currentApp?.dAppUuid && isLoggedIn) {
        const segments = location.pathname.split('/').filter(Boolean)
        const [lang, appName] = segments

        // 确保是当前应用的页面
        if (appName === currentApp.appNameEn) {
          console.log('👁️ Sidebar: 页面重新可见，刷新历史记录')
          fetchAppConversations(currentApp.dAppUuid, true) // 强制刷新
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [currentApp, isLoggedIn, location.pathname, fetchAppConversations])

  // 监听登录状态变化，仅处理退出登录的清理工作
  useEffect(() => {
    const handleLoginStatusChanged = (event: CustomEvent) => {
      console.log('📢 Sidebar: 收到登录状态变化事件:', event.detail.type)

      if (event.detail.type === 'logout') {
        // 退出登录时清空历史记录
        setAppConversations({})
        console.log('🧹 Sidebar: 已清空历史记录')
      }
      // 移除登录后的重复刷新逻辑，由ConversationRefreshManager统一处理
    }

    // 只监听登录状态变化事件
    window.addEventListener('loginStatusChanged', handleLoginStatusChanged as EventListener)

    return () => {
      window.removeEventListener('loginStatusChanged', handleLoginStatusChanged as EventListener)
    }
  }, [])

  // 注册到刷新管理器
  useEffect(() => {
    if (currentApp?.dAppUuid) {
      const componentId = `Sidebar-${currentApp.dAppUuid}`;

      // 注册刷新回调
      ConversationRefreshManager.registerRefreshCallback(componentId, () => {
        console.log('🔄 Sidebar: 收到刷新请求，清除本地缓存并重新加载历史记录');
        // 清除本地缓存时间戳，确保强制刷新
        lastFetchTimeRef.current[currentApp.dAppUuid] = 0;
        fetchAppConversations(currentApp.dAppUuid, true); // 强制刷新
      });

      return () => {
        // 组件卸载时取消注册
        ConversationRefreshManager.unregisterRefreshCallback(componentId);
      };
    }
  }, [currentApp?.dAppUuid, fetchAppConversations])

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
        refreshTimeoutRef.current = null
      }
    }
  }, [])

  // 处理对话列表更新
  const handleConversationUpdate = useCallback((updatedList: IConversationItem[]) => {
    if (currentApp?.dAppUuid) {
      setAppConversations(prev => ({
        ...prev,
        [currentApp.dAppUuid]: updatedList
      }));
    }
  }, [currentApp?.dAppUuid]);

  const handleNavClick = (appUuid: string, dAppUuid: string) => {
    if (dAppUuid) {
      // 如果是展开操作且还没有获取过该app的对话列表，则获取
      if (!appConversations[dAppUuid]) {
        fetchAppConversations(dAppUuid)
      }
      // 在移动端点击后关闭侧边栏
      if (isMobile() && onClose) {
        onClose()
      }
      // 打开会话历史弹框而不是导航
      onConversationClick()
      return
    }

    // 在移动端点击后关闭侧边栏
    if (onClose) {
      onClose()
    }
  }

  // 处理点击 Logo 回到首页 - 保持当前语言和应用
  const handleLogoClick = () => {
    // 获取当前应用名，如果无法从URL获取则使用当前应用的appNameEn
    const currentAppName = appName || currentApp?.appNameEn || 'novax-base'
    navigateToApp(currentAppName)

    // 在移动端点击后关闭侧边栏
    if (isMobile() && onClose) {
      onClose()
    }
  }

  // 处理新建聊天点击
  const handleNewChatClick = () => {
    // 获取当前应用名，如果无法从URL获取则使用当前应用的appNameEn
    const currentAppName = appName || currentApp?.appNameEn || 'novax-base'
    navigateToApp(currentAppName)

    // 在移动端点击后关闭侧边栏
    if (isMobile() && onClose) {
      onClose()
    }
  }

  // 处理知识库点击
  const handleKnowledgebaseClick = () => {
    navigateToApp('knowledgebase')

    // 在移动端点击后关闭侧边栏
    if (isMobile() && onClose) {
      onClose()
    }
  }

  // 处理意见反馈点击
  const handleFeedbackClick = () => {
    if (!isLoggedIn) {
      message.warning(t('auth.loginRequired'))
      return
    }
    setShowFeedbackModal(true)
  }

  return (
    <div
      className={`${isCollapsed ? 'w-16' : 'w-60'} h-full flex flex-col ${isCollapsed ? 'p-2' : 'p-4'} slide-in bg-white shadow-xl transition-all duration-300`}
      style={{ backgroundColor: 'var(--bg-sidebar)' }}
    >
      {/* 移动端关闭按钮 */}
      {!isCollapsed && (
        <div className="flex justify-between items-center mb-6 md:hidden">
          <h2 className="text-lg font-semibold text-gray-900"></h2>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-lg transition-colors"
            >
              ✕
            </button>
          )}
        </div>
      )}

      {/* 左侧Logo区域 */}
      <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-2 md:gap-3'} mb-6`}>
        {/* Logo - 可点击回到首页 */}
        <button
          onClick={handleLogoClick}
          className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-3'} transition-opacity cursor-pointer`}
          title={isCollapsed ? currentApp?.appName : undefined}
        >
          <div className={`${isCollapsed ? 'w-8 h-8' : 'w-10 h-10 md:w-12 md:h-12'} rounded-xl flex items-center justify-center`}>
            <img
              src={currentApp?.appIcon}
              alt={currentApp?.appName}
              className="w-full h-full object-cover rounded-xl"
            />
          </div>
          {!isCollapsed && (
            <span className="font-bold text-gray-800 text-lg sm:text-xl md:text-2xl tracking-tight">{currentApp?.appName}</span>
          )}
        </button>
      </div>

      {/* 搜索框 */}
      <button
        style={{ display: 'none' }}
        onClick={onSearchClick}
        className="flex items-center gap-3 w-full p-3 bg-white rounded-xl text-gray-600 hover:bg-gray-50 transition-colors mb-6 btn-hover shadow-sm border border-gray-200"
      >
        <div className="nav-icon">
          <span>🔍</span>
        </div>
        <span className="text-sm font-medium">{t('sidebar.search')}</span>
        <span className="ml-auto text-xs bg-gray-100 px-2 py-1 rounded-lg">⌘+K</span>
      </button>

      {/* 新建聊天按钮 */}
      <div className="mb-1">
        <button
          onClick={handleNewChatClick}
          className={`w-full flex items-center ${isCollapsed ? 'justify-center p-3' : 'gap-3 p-3'} text-gray-700 hover:text-blue-600 rounded-lg transition-all duration-200 font-medium`}
          title={isCollapsed ? t('chat.newChat') : undefined}
        >
          <div className="flex items-center justify-center w-5 h-5">
            <svg
              width={16}
              height={16}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
          </div>
          {!isCollapsed && <span>{t('chat.newChat')}</span>}
        </button>
      </div>

      {/* 知识库按钮 */}
      <div className="mb-1">
        <button
          onClick={handleKnowledgebaseClick}
          className={`w-full flex items-center ${isCollapsed ? 'justify-center p-3' : 'gap-3 p-3'} text-gray-700 hover:text-blue-600 rounded-lg transition-all duration-200 font-medium`}
          title={isCollapsed ? t('sidebar.knowledgebase') : undefined}
        >
          <div className="flex items-center justify-center w-5 h-5">
            <svg
              width={16}
              height={16}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
            </svg>
          </div>
          {!isCollapsed && <span>{t('sidebar.knowledgebase')}</span>}
        </button>
      </div>

      {/* 聊天历史区域 */}
      <div className="flex-1 mb-6">
        {/* 聊天历史标题和列表 */}
        <div className="relative">
          <div
            onClick={() => handleNavClick(currentApp.appUuid, currentApp.dAppUuid)}
            className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-3'} py-2 px-3 cursor-pointer mb-0 text-gray-700 rounded-lg transition-all duration-200 relative group`}
            title={isCollapsed ? t('sidebar.conversationHistory') : undefined}
          >
            <div className="flex items-center justify-center w-5 h-5">
              <HistoryIcon
                width={16}
                height={16}
                className="text-gray-600 group-hover:text-blue-600 transition-colors duration-200"
              />
            </div>
            {!isCollapsed && (
              <span className="font-medium text-sm text-gray-600 group-hover:text-blue-600 truncate flex-1 transition-colors duration-200">
                {t('sidebar.conversationHistory')}
              </span>
            )}
          </div>

          {!isCollapsed && (
            <div className="ml-2 mt-1 pl-2">
              <SidebarConversationList
                conversationList={appConversations[currentApp.dAppUuid] || []}
                onConversationClick={onConversationClick}
                appUuid={currentApp.appNameEn}
                onClose={onClose}
                difyApi={difyApi}
                onConversationUpdate={handleConversationUpdate}
              />
            </div>
          )}
        </div>
      </div>

      {/* 底部版权信息 */}
      <div className={`text-xs leading-5 mt-auto border-t border-gray-200 pt-3 ${isCollapsed ? 'text-center' : ''}`}>
        <div className={`${!excludedDomains.includes(currentDomain) ? 'mb-2' : 'mb-0'} font-medium flex ${isCollapsed ? 'flex-col items-center gap-3' : 'items-center gap-6'}`} style={{ color: '#A8AEB9' }}>
          {/* 关于我们 */}
          <div
            className="flex items-center cursor-pointer hover:text-blue-600 transition-colors duration-200"
            onClick={() => setShowAboutModal(true)}
            title={isCollapsed ? t('footer.aboutUs') : undefined}
          >
            <UsersIcon
              width={14}
              height={14}
              className="transition-colors duration-200"
            />
            {!isCollapsed && (
              <span className="ml-1 transition-colors duration-200">
                {t('footer.aboutUs')}
              </span>
            )}
          </div>

          {/* FAQ */}
          <div
            className="flex items-center cursor-pointer hover:text-blue-600 transition-colors duration-200"
            onClick={() => setShowFaqModal(true)}
            title={isCollapsed ? 'FAQ' : undefined}
          >
            <InfoIcon
              width={14}
              height={14}
              className="transition-colors duration-200"
            />
            {!isCollapsed && (
              <span className="ml-1 transition-colors duration-200">
                FAQ
              </span>
            )}
          </div>

          {/* 意见反馈 */}
          <div
            className="flex items-center cursor-pointer hover:text-blue-600 transition-colors duration-200"
            onClick={handleFeedbackClick}
            title={t('navigation.feedback')}
          >
            <svg
              width={14}
              height={14}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="transition-colors duration-200"
            >
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
            </svg>
          </div>
        </div>
        {/* 下行：备案信息 - 折叠时隐藏，非medxy.ai相关域名显示 */}
        {!isCollapsed && !excludedDomains.includes(currentDomain) && (
          <div className="cursor-pointer transition-colors">
            <a
              href="https://beian.miit.gov.cn/"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 hover:underline transition-all duration-200 font-medium"
              style={{ color: '#D7DBE0' }}
            >
              {t('footer.icpLicense')}
            </a>
          </div>
        )}

        <div className="cursor-pointer transition-colors mt-1">
            <a
              href="https://www.medsci.cn/about/index.do?id=18"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 hover:underline transition-all duration-200 font-medium"
              style={{ color: '#D7DBE0' }}
            >
              {t('subscription.privacyPolicy')}
            </a>
          </div>
      </div>

      {/* 关于我们弹框 */}
      <AboutUsModal
        open={showAboutModal}
        onClose={() => setShowAboutModal(false)}
      />

      {/* FAQ弹框 */}
      <FaqModal
        open={showFaqModal}
        onClose={() => setShowFaqModal(false)}
      />

      {/* 意见反馈弹窗 */}
      <FeedbackModal
        open={showFeedbackModal}
        onCancel={() => setShowFeedbackModal(false)}
      />
    </div>
  )
}

export default Sidebar