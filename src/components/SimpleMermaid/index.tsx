/**
 * 简化版 Mermaid 组件
 * 专门用于 AiResponse<PERSON><PERSON><PERSON>，确保稳定性
 */

import React, { useEffect, useState } from 'react';
import mermaid from 'mermaid';
import { useSimpleTranslation } from '../../i18n/simple-hooks';

interface SimpleMermaidProps {
  code: string;
  className?: string;
}

// 全局初始化状态
let mermaidInitialized = false;

// 友好的错误信息处理
const getFriendlyErrorMessage = (error: string, t: (key: string) => string): {
  title: string;
  message: string;
  suggestion?: string;
  originalError: string;
} => {
  const lowerError = error.toLowerCase();

  if (lowerError.includes('syntax error') || lowerError.includes('parse error')) {
    return {
      title: t('mermaid.error.syntaxError'),
      message: t('mermaid.error.syntaxErrorMessage'),
      suggestion: t('mermaid.error.syntaxErrorSuggestion'),
      originalError: error
    };
  }

  if (lowerError.includes('lexical error') || lowerError.includes('unexpected')) {
    return {
      title: t('mermaid.error.lexicalError'),
      message: t('mermaid.error.lexicalErrorMessage'),
      suggestion: t('mermaid.error.lexicalErrorSuggestion'),
      originalError: error
    };
  }

  if (lowerError.includes('timeout') || lowerError.includes('time out')) {
    return {
      title: t('mermaid.error.timeout'),
      message: t('mermaid.error.timeoutMessage'),
      suggestion: t('mermaid.error.timeoutSuggestion'),
      originalError: error
    };
  }

  if (lowerError.includes('empty') || lowerError.includes('为空')) {
    return {
      title: t('mermaid.error.emptyCode'),
      message: t('mermaid.error.emptyCodeMessage'),
      suggestion: t('mermaid.error.emptyCodeSuggestion'),
      originalError: error
    };
  }

  // 默认错误处理
  return {
    title: t('mermaid.error.renderFailed'),
    message: t('mermaid.error.renderFailedMessage'),
    suggestion: t('mermaid.error.renderFailedSuggestion'),
    originalError: error
  };
};

// 格式化 Mermaid 代码，处理注释和连接线的换行问题，同时保留重要的空行
const formatMermaidCode = (code: string): string => {
  // 按行分割，保留空行但去除每行的前后空白
  const lines = code.split('\n').map(line => line.trim());
  const formattedLines: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // 保留空行，特别是样式定义前后的空行
    if (line === '') {
      // 检查空行是否有意义（例如分隔样式定义）
      const prevLine = i > 0 ? lines[i - 1] : '';
      const nextLine = i < lines.length - 1 ? lines[i + 1] : '';

      // 如果前一行或后一行包含样式定义关键字，保留空行
      const isStyleSeparator =
        prevLine.includes('classDef') ||
        prevLine.includes('class ') ||
        prevLine.includes('style ') ||
        nextLine.includes('classDef') ||
        nextLine.includes('class ') ||
        nextLine.includes('style ') ||
        prevLine.includes('%%') ||
        nextLine.includes('%%');

      if (isStyleSeparator) {
        formattedLines.push('');
      }
      continue;
    }

    // 1. 处理行内注释 - 将 "语句 %% 注释" 分成两行
    if (line.includes('%%')) {
      const commentIndex = line.indexOf('%%');
      const beforeComment = line.substring(0, commentIndex).trim();
      const comment = line.substring(commentIndex).trim();

      if (beforeComment) {
        // 有语句在注释前，分成两行
        formattedLines.push(beforeComment);
        formattedLines.push(comment);
      } else {
        // 注释已经在行首，保持原样
        formattedLines.push(comment);
      }
      continue;
    }

    // 2. 处理连续的连接语句 - 将 "A --> B C --> D" 分成多行
    // 检查是否有多个箭头符号
    const arrowCount = (line.match(/-->|---|\.\.\.|===>?/g) || []).length;

    if (arrowCount > 1) {
      // 使用更简单的分割策略：在节点后面跟着空格和下一个节点的地方分割
      // 匹配模式：节点（可能有标签）+ 空格 + 下一个节点开始
      const splitPattern = /(\w+(?:\[.*?\])?\s*(?:-->|---|\.\.\.|===>?)\s*\w+(?:\[.*?\])?)\s+(?=\w+\s*(?:-->|---|\.\.\.|===>?))/g;

      let lastIndex = 0;
      let match;
      const parts: string[] = [];

      while ((match = splitPattern.exec(line)) !== null) {
        // 添加匹配的连接语句
        parts.push(match[1].trim());
        lastIndex = match.index + match[1].length;
      }

      // 添加剩余部分
      if (lastIndex < line.length) {
        const remaining = line.substring(lastIndex).trim();
        if (remaining) {
          parts.push(remaining);
        }
      }

      if (parts.length > 1) {
        parts.forEach(part => formattedLines.push(part));
      } else {
        // 如果分割失败，尝试简单的空格分割
        const simpleParts = line.split(/\s+(?=\w+\s*(?:-->|---|\.\.\.|===>?))/);
        if (simpleParts.length > 1) {
          simpleParts.forEach(part => {
            const trimmed = part.trim();
            if (trimmed) formattedLines.push(trimmed);
          });
        } else {
          formattedLines.push(line);
        }
      }
    } else {
      // 单个连接或非连接语句，保持原样
      formattedLines.push(line);
    }
  }

  return formattedLines.join('\n');
};

// 处理 Mermaid 代码中的 Markdown 格式和文本换行
const processMermaidMarkdown = (code: string): string => {
  let processedCode = code;

  // 处理粗体 **text** -> <b>text</b>
  processedCode = processedCode.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>');

  // 处理斜体 *text* -> <i>text</i>
  processedCode = processedCode.replace(/\*(.*?)\*/g, '<i>$1</i>');

  // 处理代码 `code` -> <code>code</code>
  processedCode = processedCode.replace(/`([^`]+)`/g, '<code>$1</code>');

  // 处理删除线 ~~text~~ -> <s>text</s>
  processedCode = processedCode.replace(/~~(.*?)~~/g, '<s>$1</s>');

  // 暂时禁用长文本换行处理，避免破坏正常的节点标签
  /*
  processedCode = processedCode.replace(/\[([^\]]{40,})\]/g, (_, content) => {
    const maxLength = 30; // 每行最大字符数，更宽松

    // 检查是否包含题号模式（如：1.1、2.3、A1、B2等）
    const titlePattern = /^(\d+\.?\d*\.?\s*|[A-Z]\d*\.?\s*)/;
    const titleMatch = content.match(titlePattern);

    if (titleMatch) {
      // 有题号的情况：保持题号和第一部分内容在一起
      const titlePart = titleMatch[0];
      const restContent = content.substring(titlePart.length);

      if (content.length <= maxLength * 1.5) {
        return `[${content}]`; // 不需要换行，给更多容忍度
      }

      // 找到第一个合适的断点（空格、标点符号等）
      let firstLineEnd = maxLength - titlePart.length;
      const breakPoints = [' ', '，', '。', '：', '；', '、'];

      // 如果剩余内容不长，就不要强制换行
      if (restContent.length <= maxLength * 0.8) {
        return `[${content}]`; // 保持在一行
      }

      for (let i = firstLineEnd; i >= titlePart.length / 2; i--) {
        if (breakPoints.includes(restContent[i])) {
          firstLineEnd = i + 1;
          break;
        }
      }

      const firstLine = titlePart + restContent.substring(0, firstLineEnd);
      const remainingText = restContent.substring(firstLineEnd);

      if (remainingText.length === 0) {
        return `[${firstLine}]`;
      }

      // 处理剩余文本
      const lines = [firstLine];
      let currentPos = 0;

      while (currentPos < remainingText.length) {
        let lineEnd = Math.min(currentPos + maxLength, remainingText.length);

        // 寻找合适的断点
        if (lineEnd < remainingText.length) {
          for (let i = lineEnd; i >= currentPos + maxLength / 2; i--) {
            if (breakPoints.includes(remainingText[i])) {
              lineEnd = i + 1;
              break;
            }
          }
        }

        lines.push(remainingText.substring(currentPos, lineEnd).trim());
        currentPos = lineEnd;
      }

      return `[${lines.filter(line => line.length > 0).join('<br/>')}]`;
    } else {
      // 没有题号的普通文本处理
      const words = content.split(/(\s+|，|。|：|；|、)/);
      let lines = [];
      let currentLine = '';

      for (const word of words) {
        if ((currentLine + word).length > maxLength && currentLine.length > 0) {
          lines.push(currentLine.trim());
          currentLine = word;
        } else {
          currentLine += word;
        }
      }
      if (currentLine.trim()) {
        lines.push(currentLine.trim());
      }

      return `[${lines.join('<br/>')}]`;
    }
  });
  */

  return processedCode;
};

const SimpleMermaid: React.FC<SimpleMermaidProps> = ({ code, className = '' }) => {
  const { t } = useSimpleTranslation();
  const [svg, setSvg] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 错误处理相关的state - 移到组件顶层
  const [showDetails, setShowDetails] = useState(false);
  const [showOriginalError, setShowOriginalError] = useState(false);
  const [copied, setCopied] = useState(false);

  // 简化的错误隐藏机制 - 只在有SVG内容时执行一次
  useEffect(() => {
    if (!svg) return;

    const hideObviousErrors = () => {
      // 只处理当前组件的容器，避免影响其他组件
      const container = document.querySelector(`.simple-mermaid-container:has(svg)`);
      if (!container) return;

      const errorTexts = container.querySelectorAll('svg text');
      errorTexts.forEach(textEl => {
        const text = textEl.textContent || '';
        if (text.includes('Syntax error in text') ||
            text.includes('mermaid version') ||
            text.includes('Parse error')) {
          (textEl as unknown as HTMLElement).style.display = 'none';
        }
      });
    };

    // 延时执行，确保SVG已经完全渲染
    const timer = setTimeout(hideObviousErrors, 150);

    return () => {
      clearTimeout(timer);
    };
  }, [svg]); // 只在svg内容变化时执行



  useEffect(() => {
    const render = async () => {
      setLoading(true);
      setError('');
      setSvg('');

      try {
        // 初始化 Mermaid（只初始化一次）
        if (!mermaidInitialized) {
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'inherit',
            htmlLabels: true, // 启用 HTML 标签支持
            flowchart: {
              htmlLabels: true
            }
          });
          mermaidInitialized = true;
        }

        // 验证代码
        if (!code || !code.trim()) {
          throw new Error(t('mermaid.error.emptyCode'));
        }

        // 清理和格式化代码
        let cleanCode = code.trim();

        // 1. 预处理：确保样式定义有适当的分隔
        cleanCode = cleanCode.replace(/(classDef|class |style )/g, '\n$1');
        cleanCode = cleanCode.replace(/(\n\s*)(classDef|class |style )/g, '$1\n$2');

        // 2. 格式化 Mermaid 语法（处理注释和连接线换行，保留重要空行）
        cleanCode = formatMermaidCode(cleanCode);

        // 3. 处理其他格式问题
        cleanCode = cleanCode.replace(/(\]\s*)([A-Z]\d*\[)/g, '$1\n$2');

        // 4. 处理 Markdown 格式
        cleanCode = processMermaidMarkdown(cleanCode);

        // 5. 最终清理：移除多余的连续空行，但保留单个空行
        cleanCode = cleanCode.replace(/\n\s*\n\s*\n/g, '\n\n');

        // 生成唯一ID
        const id = `simple-mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        // 渲染
        const { svg } = await mermaid.render(id, cleanCode);
        setSvg(svg);

        // 渲染完成，错误隐藏由 useEffect 统一处理


      } catch (err: any) {
        setError(err.message || t('mermaid.error.renderFailed'));
      } finally {
        setLoading(false);
      }
    };

    render();
  }, [code, t]);

  // 弹窗组件
  const Modal = () => {
    const [scale, setScale] = useState(1.5); // 默认放大到 150%
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

    if (!isModalOpen) return null;

    const handleZoomIn = () => setScale(prev => Math.min(prev + 0.25, 5)); // 最大 500%
    const handleZoomOut = () => setScale(prev => Math.max(prev - 0.25, 0.3)); // 最小 30%

    // 键盘快捷键支持
    React.useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setIsModalOpen(false);
        } else if (e.ctrlKey || e.metaKey) {
          switch (e.key) {
            case '=':
            case '+':
              e.preventDefault();
              handleZoomIn();
              break;
            case '-':
              e.preventDefault();
              handleZoomOut();
              break;
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }, []);

    const handleMouseDown = (e: React.MouseEvent) => {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    };

    const handleMouseMove = (e: React.MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y
        });
      }
    };

    const handleMouseUp = () => setIsDragging(false);

    return (
      <div className="fixed inset-0 z-[9999] bg-black bg-opacity-90">
        {/* 工具栏 - 固定在顶部 */}
        <div className="absolute top-0 left-0 right-0 z-[10000] bg-white border-b border-gray-200 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <h3 className="text-xl font-semibold text-gray-900">Mermaid 图表</h3>
            <div className="flex items-center space-x-3">
              {/* 缩放控制 */}
              <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={handleZoomOut}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-white rounded transition-colors"
                  title="缩小 (Ctrl + -)"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>

                <span className="text-sm font-medium text-gray-700 min-w-[70px] text-center px-2">
                  {Math.round(scale * 100)}%
                </span>

                <button
                  onClick={handleZoomIn}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-white rounded transition-colors"
                  title="放大 (Ctrl + +)"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>

              <div className="w-px h-8 bg-gray-300" />

              {/* 下载按钮 */}
              <button
                onClick={() => {
                  const blob = new Blob([svg], { type: 'image/svg+xml' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = 'mermaid-diagram.svg';
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }}
                className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
                title="下载 SVG"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>

              {/* 关闭按钮 */}
              <button
                onClick={() => setIsModalOpen(false)}
                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                title="关闭 (ESC)"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* 图表容器 - 占满整个屏幕 */}
        <div
          className="absolute inset-0 pt-20 overflow-hidden bg-gray-100"
          onClick={(e) => {
            // 点击空白区域关闭弹窗
            if (e.target === e.currentTarget) {
              setIsModalOpen(false);
            }
          }}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <div
            className={`w-full h-full flex items-center justify-center ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
            style={{
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
              transformOrigin: 'center center'
            }}
            onMouseDown={handleMouseDown}
          >
            <div
              className="bg-white p-8 rounded-lg shadow-2xl max-w-none"
              style={{
                minWidth: '60vw',
                minHeight: '40vh'
              }}
              dangerouslySetInnerHTML={{ __html: svg }}
            />
          </div>
        </div>

        {/* 底部提示信息 */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-[10000]">
          <div className="bg-black bg-opacity-80 text-white text-sm px-6 py-3 rounded-lg backdrop-blur-sm">
            <div className="flex items-center space-x-4 text-center">
              <span>🖱️ 拖拽移动</span>
              <span>•</span>
              <span>🔍 +/- 缩放</span>
              <span>•</span>
              <span>ESC 关闭</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`p-4 text-center border border-gray-200 rounded-lg bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center space-x-2">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
          <span className="text-gray-600">{t('mermaid.rendering')}</span>
        </div>
      </div>
    );
  }

  // 复制代码的处理函数
  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // 静默处理复制失败，避免控制台噪音
    }
  };

  if (error) {
    const friendlyError = getFriendlyErrorMessage(error, t);

    return (
      <div className={`border border-red-200 bg-red-50 rounded-lg p-4 ${className}`}>
        <div className="flex items-start space-x-3">
          <svg className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800">{friendlyError.title}</h3>
            <p className="mt-1 text-sm text-red-700">{friendlyError.message}</p>

            {friendlyError.suggestion && (
              <div className="mt-2 p-2 bg-red-100 rounded border border-red-200">
                <p className="text-xs text-red-600">
                  <span className="font-medium">{t('mermaid.suggestion')}：</span>
                  {friendlyError.suggestion}
                </p>
              </div>
            )}

            <div className="mt-3">
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 border border-red-300 rounded hover:bg-red-200 transition-colors"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
                {showDetails ? t('mermaid.hideCode') : t('mermaid.viewCode')}
              </button>
            </div>

            {showDetails && (
              <div className="mt-3 bg-gray-50 border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-white px-3 py-2 flex items-center justify-between border-b border-gray-200">
                  <span className="text-xs font-medium text-gray-600">{t('mermaid.sourceCode')}</span>
                  <button
                    onClick={handleCopyCode}
                    className="inline-flex items-center px-2 py-1 text-xs text-gray-500 hover:text-gray-700 bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                  >
                    {copied ? (
                      <>
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        {t('mermaid.copied')}
                      </>
                    ) : (
                      <>
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        {t('mermaid.copy')}
                      </>
                    )}
                  </button>
                </div>
                <pre className="bg-gray-200 text-gray-800 p-4 text-xs overflow-x-auto leading-relaxed font-mono">
                  <code>{code}</code>
                </pre>

                <div className="bg-white px-3 py-2 border-t border-gray-200">
                  <button
                    onClick={() => setShowOriginalError(!showOriginalError)}
                    className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    {showOriginalError ? t('mermaid.hideOriginalError') : t('mermaid.viewOriginalError')}
                  </button>
                  {showOriginalError && (
                    <div className="mt-2 p-3 bg-gray-100 rounded text-xs text-gray-600 font-mono border border-gray-200">
                      {friendlyError.originalError}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={`simple-mermaid-container border border-gray-200 rounded-lg p-4 bg-white overflow-x-auto relative group ${className}`}>
        <div
          className="cursor-pointer"
          onClick={() => setIsModalOpen(true)}
          dangerouslySetInnerHTML={{ __html: svg }}
        />
        
        {/* 悬浮工具栏 */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={() => setIsModalOpen(true)}
            className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 text-gray-500 hover:text-gray-700"
            title="点击放大查看"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
            </svg>
          </button>
        </div>
        
        {/* 点击提示 */}
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            点击放大查看
          </div>
        </div>
      </div>
      
      <Modal />
    </>
  );
};

export default SimpleMermaid;
