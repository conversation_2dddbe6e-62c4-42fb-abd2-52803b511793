import type React from 'react';
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeftIcon } from '../icons/Icons';
import { useSimpleTranslation, useI18nRouter } from '../../i18n/simple-hooks';

interface ChatDetailHeaderProps {
  title?: string;
  showBackButton?: boolean;
  appIcon?: string;
  currentAppName?: string;
}

/**
 * 聊天详情页头部组件
 * 包含返回按钮和页面标题
 */
const ChatDetailHeader: React.FC<ChatDetailHeaderProps> = ({
  title = 'NovaX AI',
  showBackButton = true,
  appIcon,
  currentAppName
}) => {
  const { t } = useSimpleTranslation();
  const { navigateToApp } = useI18nRouter();
  const navigate = useNavigate();
  const { lang, appName } = useParams<{
    lang?: string;
    appName?: string;
  }>();

  // 滚动状态管理
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // 向下滚动超过100px时隐藏，向上滚动时显示
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    // 添加滚动监听器
    window.addEventListener('scroll', handleScroll, { passive: true });

    // 清理函数
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [lastScrollY]);

  // 返回功能
  const handleGoBack = () => {
    // 如果有应用名称，返回到应用首页
    if (appName) {
      navigateToApp(appName);
    } else {
      // 否则返回到主页
      navigateToApp('novax-base');
    }
  };

  return (
    <>
      {/* 主头部 - 滚动时隐藏 */}
      <div className={`md:hidden fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200/50 px-4 sm:px-6 py-3 shadow-sm transition-transform duration-300 ease-in-out ${
        isVisible ? 'transform translate-y-0' : 'transform -translate-y-full'
      }`}>
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <div className="flex items-center gap-3">
            {/* 返回按钮 */}
            {showBackButton && (
              <button
                onClick={handleGoBack}
                className="
                  flex items-center justify-center w-9 h-9
                  text-gray-600 hover:text-gray-800
                  rounded-lg 
                  transition-all duration-200
                  hover:scale-105 active:scale-95
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
                "
                title={t('common.back')}
              >
                <ArrowLeftIcon
                  className='md:hidden'
                  width={18}
                  height={18}
                  stroke="currentColor"
                  strokeWidth={2}
                />
              </button>
            )}

            {/* 应用标题 */}
            <div className="flex items-center gap-2">
              <div className="w-7 h-7 md:w-12 md:h-12 from-gray-800 to-black rounded-lg flex items-center justify-center shadow-sm overflow-hidden">
                {appIcon ? (
                  <a href={location.href.substr(0,location.href.lastIndexOf('/'))}>
                    <img
                    src={appIcon}
                    alt={currentAppName || title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // 图片加载失败时显示默认的 N 图标
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = '<span class="text-white text-sm font-bold">N</span>';
                      }
                    }}
                  />
                  </a>
                ) : (
                  <span className="text-white text-sm font-bold">N</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 浮动返回按钮 - 头部隐藏时显示 */}
      {showBackButton && !isVisible && (
        <button
          onClick={handleGoBack}
          className="
            md:hidden
            fixed top-3 left-3 sm:top-4 sm:left-4 z-50
            flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10
            text-gray-600 hover:text-gray-800
            bg-white/95 hover:bg-white
            rounded-full border border-gray-200
            shadow-lg hover:shadow-xl
            backdrop-blur-md
            transition-all duration-300 ease-in-out
            hover:scale-110 active:scale-95
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
            animate-in fade-in slide-in-from-left-2 duration-300
          "
          title={t('common.back')}
        >
          <ArrowLeftIcon
            width={18}
            height={18}
            stroke="currentColor"
            strokeWidth={2.5}
            className="sm:w-5 sm:h-5"
          />
        </button>
      )}
    </>
  );
};

export default ChatDetailHeader;
