import type React from 'react'
import { useRef, useState, useCallback, useEffect } from 'react'
import { message, Modal } from 'antd'
import type { ChatUploadFileItem } from './ChatFileUpload'
import { useSimpleTranslation } from '../../i18n/simple-hooks'
import FileUploadStatus, { FileUploadStatusItem } from '../file-upload/FileUploadStatus'
import '../file-upload/FileUpload.css'
import type { Mode } from '../common/ModeSelector'
import { useCompetition } from '../../contexts/CompetitionContext'

interface ChatInputProps {
  inputValue: string
  isGenerating: boolean
  onInputChange: (value: string) => void
  onSubmit: () => void
  onKeyDown: (e: React.KeyboardEvent) => void
  onStop: () => void
  // 新增：文件上传相关props
  files?: ChatUploadFileItem[]
  onFilesChange?: (files: ChatUploadFileItem[]) => void
  onFileUpload?: (files: File[]) => Promise<void>
  allowedFileTypes?: string[]
  isFileUploadEnabled?: boolean
  isUploading?: boolean
  maxFileSize?: number
  maxFiles?: number
  onValidationError?: (error: string) => void
  // 新增：模式选择相关props
  currentAppName?: string
  selectedMode?: Mode
  onModeChange?: (mode: Mode) => void
}

/**
 * 聊天输入组件 - 处理用户输入和消息发送
 * 包含文本输入框、发送按钮、快捷键提示和文件上传功能
 */
const ChatInput: React.FC<ChatInputProps> = ({
  inputValue,
  isGenerating,
  onInputChange,
  onSubmit,
  onKeyDown,
  onStop,
  files = [],
  onFilesChange,
  onFileUpload,
  allowedFileTypes = [],
  isFileUploadEnabled = false,
  isUploading = false,
  maxFileSize = 50,
  maxFiles,
  onValidationError,
  currentAppName,
  selectedMode = '简单模式',
  onModeChange
}) => {
  const { t } = useSimpleTranslation()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragOver, setIsDragOver] = useState(false)
  const [dragCounter, setDragCounter] = useState(0)
  const [showModeMenu, setShowModeMenu] = useState(false)

  // 使用全局比赛状态
  const {
    competitionStarted,
    competitionCompleted,
    hasCompetitionPermission,
    isPermissionLoading,
    startCompetition
  } = useCompetition();

  // 开始比赛流程 - 只有在有权限时才允许开始
  const handleStartCompetition = () => {
    if (hasCompetitionPermission) {
      startCompetition(5400) // 开始90分钟的比赛
    } else {
      console.warn('用户没有比赛权限，无法开始比赛');
    }
  };

  // 跳转到调研问卷
  const handleShowSurvey = () => {
    // 直接打开调研问卷链接
    location.href = "https://www.medsci.cn/form/detail.do?id=2c03595d9"
  };

  // 模式配置数据 - 与MainContent.tsx保持一致
  const modeOptions = [
    {
      value: '简单模式' as Mode,
      labelKey: 'mode.simple',
      descKey: 'mode.simpleDescription',
      icon: (
        <svg className="w-[18px] h-[18px]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    },
    {
      value: '精细模式' as Mode,
      labelKey: 'mode.detailed',
      descKey: 'mode.detailedDescription',
      icon: (
        <svg className="w-[18px] h-[18px]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      )
    }
  ]

  // 获取当前选中模式的配置
  const currentModeConfig = modeOptions.find(mode => mode.value === selectedMode) || modeOptions[0]

  // 生成accept属性字符串
  const acceptString = allowedFileTypes.length > 0
    ? allowedFileTypes.map(ext => `.${ext}`).join(',')
    : '.pdf,.doc,.docx,.txt,.md,.xlsx,.xls,.pptx,.ppt,.csv,.html,.xml,.epub,.json,.jpg,.jpeg,.png,.gif,.webp,.svg,.mp3,.m4a,.wav,.webm,.amr,.mp4,.mov,.mpeg,.mpga'

  // 确保文件输入元素在组件挂载后立即可用
  useEffect(() => {
    if (isFileUploadEnabled && fileInputRef.current) {
      // 确保文件输入元素的属性正确设置
      const input = fileInputRef.current
      if (!input.multiple) input.multiple = true
    }
  }, [isFileUploadEnabled])

  // 拖拽处理函数
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!isFileUploadEnabled || isGenerating || competitionCompleted) return

    setDragCounter(prev => prev + 1)
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true)
    }
  }, [isFileUploadEnabled, isGenerating, competitionCompleted])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!isFileUploadEnabled || isGenerating || competitionCompleted) return

    setDragCounter(prev => {
      const newCounter = prev - 1
      if (newCounter === 0) {
        setIsDragOver(false)
      }
      return newCounter
    })
  }, [isFileUploadEnabled, isGenerating, competitionCompleted])

  // 文件验证函数
  const validateFiles = useCallback((newFiles: File[]): { validFiles: File[], errors: string[] } => {
    const validFiles: File[] = []
    const errors: string[] = []

    // 检查当前已上传文件数量 + 新选择文件数量是否超过限制
    const currentFileCount = files.length // 当前已上传的文件数量
    const newFileCount = newFiles.length // 新选择的文件数量
    const maxFilesLimit = maxFiles || 2 // 默认限制为2个文件
    const availableSlots = maxFilesLimit - currentFileCount

    if (availableSlots <= 0) {
      errors.push(t('chat.maxFilesReached').replace('{count}', maxFilesLimit.toString()))
      return { validFiles: [], errors }
    }

    if (newFileCount > availableSlots) {
      errors.push(t('chat.maxFilesExceeded')
        .replace('{available}', availableSlots.toString())
        .replace('{max}', maxFilesLimit.toString()))
      return { validFiles: [], errors }
    }

    // 限制批量选择文件数量（使用动态配置值）
    if (newFileCount > maxFilesLimit) {
      errors.push(t('chat.batchUploadLimit').replace('{count}', maxFilesLimit.toString()))
      return { validFiles: [], errors }
    }

    // 检查文件类型和大小
    for (const file of newFiles) {
      // 检查文件大小
      const fileSizeMB = file.size / (1024 * 1024)
      if (fileSizeMB > maxFileSize) {
        errors.push(t('chat.fileSizeExceeded')
          .replace('{fileName}', file.name)
          .replace('{maxSize}', maxFileSize.toString()))
        continue
      }

      // 检查文件类型
      if (allowedFileTypes.length > 0) {
        const fileExtension = file.name.split('.').pop()?.toLowerCase()
        if (!fileExtension || !allowedFileTypes.includes(fileExtension)) {
          errors.push(t('chat.unsupportedFileType')
            .replace('{fileName}', file.name)
            .replace('{formats}', allowedFileTypes.join(', ')))
          continue
        }
      }

      validFiles.push(file)
    }

    return { validFiles, errors }
  }, [files.length, maxFiles, maxFileSize, allowedFileTypes])

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!isFileUploadEnabled || isGenerating || competitionCompleted) return

    setIsDragOver(false)
    setDragCounter(0)

    const droppedFiles = Array.from(e.dataTransfer.files)
    if (droppedFiles.length > 0 && onFileUpload) {
      const { validFiles, errors } = validateFiles(droppedFiles)

      if (errors.length > 0) {
        errors.forEach(error => {
          message.error(error)
          onValidationError?.(error)
        })
      }

      if (validFiles.length > 0) {
        await onFileUpload(validFiles)
      }
    }
  }, [isFileUploadEnabled, isGenerating, competitionCompleted, onFileUpload, validateFiles, onValidationError])

  return (
    <div className="sticky bottom-0 py-1 pb-2 px-6 backdrop-blur-sm z-40" style={{ backgroundColor: 'var(--bg-main)' }}>
      <div className="max-w-4xl mx-auto">
        {/* 主输入框区域 */}
        <div
          className={`relative bg-white rounded-2xl shadow-lg border transition-all duration-300 ${
            isDragOver
              ? 'border-blue-500 bg-blue-50 shadow-2xl ring-2 ring-blue-200'
              : 'border-gray-200'
          }`}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {/* 比赛状态蒙层 */}
          {hasCompetitionPermission && !isPermissionLoading && (
            <>
              {/* 比赛未开始 - 显示开始按钮 */}
              {!competitionStarted && !competitionCompleted && (
                <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 to-indigo-900/0 rounded-2xl flex items-center justify-center z-50 backdrop-blur-sm">
                  <div className="text-center">
                    <button
                      onClick={handleStartCompetition}
                      className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold text-base rounded-[6px]  transform hover:scale-105 transition-all duration-300 ease-in-out flex items-center justify-center gap-2 min-w-36"
                    >
                      {t('competition.startCompetition')}
                    </button>
                  </div>
                </div>
              )}

              {/* 比赛已结束 - 显示结束状态 */}
              {competitionCompleted && (
                <div className="absolute inset-0 bg-gradient-to-br from-green-900/10 to-indigo-900/0 rounded-2xl flex items-center justify-center z-50 backdrop-blur-sm">
                  <div className="text-center">
                    <div className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold text-base rounded-[6px]  flex mx-auto items-center justify-center w-36 cursor-not-allowed opacity-80">
                      {t('competition.competitionEnded')}
                    </div>
                    <p className="text-gray-700 text-sm mt-3 font-medium">
                      {t('competition.congratulations')}
                      <button
                        onClick={handleShowSurvey}
                        className="text-blue-600 hover:text-blue-800 underline cursor-pointer transition-colors duration-200 mx-1"
                      >
                        {t('competition.surveyLink')}
                      </button>
                      {/* {t('competition.congratulationsEnd')} */}
                    </p>
                  </div>
                </div>
              )}
            </>
          )}
          {/* 文件列表显示区域 - 在输入框上方 */}
          {isFileUploadEnabled && files.length > 0 && (
            <div className="p-4 pb-2 border-b border-gray-100">
              <FileUploadStatus
                files={files.map(file => ({
                  uid: file.uid,
                  name: file.name,
                  status: file.status,
                  size: file.size,
                  type: file.type,
                  percent: file.percent,
                  error: file.error,
                  upload_file_id: file.upload_file_id
                }))}
                onRemove={(uid) => {
                  Modal.confirm({
                    title: t('fileUpload.removeFile'),
                    content: t('fileUpload.confirmRemove'),
                    onOk: () => {
                      const updatedFiles = files.filter(f => f.uid !== uid)
                      onFilesChange?.(updatedFiles)
                    },
                    onCancel: () => {},
                  });
                }}
                showProgress={true}
                compact={true}
                layout="horizontal"
                maxFileNameLength={15}
              />
            </div>
          )}

          {/* 文件输入元素 */}
          {isFileUploadEnabled && (
            <>
              {/* 主要的隐藏文件输入 */}
              <input
                ref={fileInputRef}
                type="file"
                multiple={(maxFiles || 2) - files.length > 1} // 只有剩余槽位大于1时才允许多选
                disabled={isGenerating || files.length >= (maxFiles || 2) || competitionCompleted} // 达到限制时、正在生成时或比赛结束时禁用
                accept={acceptString}
                onChange={async (e) => {
                  if (isGenerating) {
                    return
                  }
                  const selectedFiles = Array.from(e.target.files || [])
                  if (selectedFiles.length > 0 && onFileUpload) {
                    const { validFiles, errors } = validateFiles(selectedFiles)

                    if (errors.length > 0) {
                      errors.forEach(error => {
                        message.error(error)
                        onValidationError?.(error)
                      })
                    }

                    if (validFiles.length > 0) {
                      try {
                        await onFileUpload(validFiles)
                      } catch (error) {
                        console.error('文件上传失败:', error)
                      }
                    }
                  }
                  // 清空input值，允许重复选择同一文件
                  e.target.value = ''
                }}
                style={{
                  position: 'absolute',
                  left: '-9999px',
                  opacity: 0,
                  pointerEvents: 'none',
                  width: '1px',
                  height: '1px'
                }}
              />


            </>
          )}

          {/* 拖拽提示覆盖层 */}
          {isDragOver && (
            <div className="absolute inset-0 bg-blue-50 bg-opacity-90 rounded-2xl flex items-center justify-center z-10 border-2 border-dashed border-blue-400">
              <div className="text-center p-6">
                <div className="text-4xl mb-3 file-icon-bounce">📎</div>
                <div className="text-blue-600 font-semibold text-lg mb-2">{t('fileUpload.dropToUpload')}</div>
                <div className="text-sm text-blue-500 space-y-1">
                  {maxFiles && (
                    <div className="font-medium">
                      {t('fileUpload.maxFiles').replace('{count}', maxFiles.toString())}
                    </div>
                  )}
                  <div>
                    {allowedFileTypes.length > 0 ? (() => {
                      // 对话详情页格式筛选：仅显示文档类格式
                      const documentFormats = ['pdf', 'docx', 'txt', 'md', 'xlsx', 'xls', 'pptx', 'ppt', 'csv', 'html', 'xml', 'epub', 'json'];
                      const filteredFormats = allowedFileTypes.filter(type =>
                        documentFormats.includes(type.toLowerCase())
                      );
                      return filteredFormats.length > 0
                        ? `${t('fileUpload.supportedFormats')}: ${filteredFormats.join(', ')}`
                        : t('fileUpload.multiFileSupport');
                    })() : t('fileUpload.multiFileSupport')}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 文本输入区域 */}
          <textarea
            value={inputValue}
            onChange={(e) => onInputChange(e.target.value)}
            onKeyDown={onKeyDown}
            placeholder={isDragOver ? "" : t('chat.inputPlaceholderDetail')}
            className={`w-full min-h-[140px] p-6 bg-transparent border-0 rounded-2xl resize-none focus:outline-none text-gray-800 text-base leading-relaxed placeholder-gray-400 ${
              isFileUploadEnabled ? 'pl-6 ' : ''
            }`}
            disabled={isGenerating}
          />

          {/* 左侧工具栏 */}
          <div className="absolute bottom-4 left-6 flex items-center gap-3">
            {/* 模式选择器 - 仅在novax-pro应用中显示，放在文件上传按钮左侧 */}
            {currentAppName === 'novax-pro' && onModeChange && (
              <div className="relative">
                <button
                  onClick={() => setShowModeMenu(!showModeMenu)}
                  className="flex items-end justify-center gap-1 h-10 text-gray-500 hover:text-gray-700 transition-colors duration-200 ease-in-out"
                >
                  {/* 选中模式的图标 */}
                  <span className="text-gray-600">
                    {currentModeConfig.icon}
                  </span>
                  {/* 下拉箭头 */}
                  <span className={`transform transition-transform duration-200`}>
                    <svg className={`w-3 h-3 text-gray-500 transition-colors duration-300 transition-transform ${showModeMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                </button>

                {/* 模式选择弹出框 - 与MainContent.tsx保持一致的样式 */}
                {showModeMenu && (
                  <div className="absolute bottom-full left-0 mb-2 bg-white border-radius-16 border border-gray-200 shadow-xl z-30 w-full max-w-[320px] md:max-w-[520px] min-w-[280px] md:min-w-[480px] fade-in p-3 md:p-6"
                       style={{
                         borderRadius: '16px',
                         border: '1px solid #e5e7eb',
                         boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                       }}>
                    {/* 横向布局的选项容器 - 桌面端宽度优化 */}
                    <div className="flex flex-row gap-2 md:gap-6">
                      {modeOptions.map((mode) => {
                        const isSelected = selectedMode === mode.value
                        return (
                          <div
                            key={mode.value}
                            onClick={() => {
                              onModeChange(mode.value)
                              setShowModeMenu(false)
                            }}
                            className={`flex-1 flex flex-col items-center justify-center p-2 md:p-4 rounded-xl transition-all duration-200 ease-in-out cursor-pointer text-center border min-h-[80px] md:min-h-[120px] ${
                              isSelected
                                ? 'bg-blue-50 border-blue-200 text-blue-600'
                                : 'border-transparent hover:bg-gray-50 hover:border-gray-200'
                            } hover:transform hover:-translate-y-0.5`}
                            style={{
                              borderRadius: '12px',
                              transition: 'all 0.2s ease-in-out'
                            }}
                          >
                            {/* 模式图标 */}
                            <div className={`mb-1 md:mb-3 flex-shrink-0 ${isSelected ? 'text-blue-600' : 'text-gray-600'}`}>
                              {mode.icon}
                            </div>
                            {/* 模式名称 */}
                            <div className={`text-xs md:text-sm font-bold mb-0.5 md:mb-2 whitespace-nowrap ${
                              isSelected ? 'text-blue-600' : 'text-gray-700'
                            }`}>
                              {t(mode.labelKey)}
                            </div>
                            {/* 模式描述 */}
                            <div className={`text-xs leading-tight md:leading-relaxed text-center px-1 md:px-2 ${
                              isSelected ? 'text-blue-500' : 'text-gray-500'
                            }`}>
                              {t(mode.descKey)}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 文件上传按钮 */}
            {isFileUploadEnabled && (
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  if (fileInputRef.current) {
                    // 使用setTimeout确保DOM更新完成
                    setTimeout(() => {
                      fileInputRef.current?.click()
                    }, 0)
                  }
                }}
                disabled={isGenerating || files.length >= (maxFiles || 2) || competitionCompleted}
                className="flex !ml-0 !pl-0 items-end justify-center h-10 rounded-full text-gray-500 hover:text-gray-700 focus:ring-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                title={t('fileUpload.tooltip')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"/>
                </svg>
              </button>
            )}
          </div>

          {/* 右侧按钮区域 */}
          <div className="absolute bottom-4 right-4 flex items-center gap-3">
            <div className="text-xs text-gray-400 hidden sm:block">
              {t('chat.enterToSend')}
            </div>
            <button
              type="submit"
              onClick={isGenerating?onStop:onSubmit}
              disabled={isUploading || (!isGenerating && !inputValue.trim())}
              className="relative flex items-center justify-center w-10 h-10 rounded-full text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed group shadow-lg hover:shadow-xl transition-all duration-200 ease-out transform hover:scale-105 bg-gradient-to-br from-purple-500 to-blue-500 hover:from-purple-400 hover:to-blue-400 focus:ring-purple-500 hover:shadow-purple-500/30"
            >
              {isGenerating ? (
                <div className="flex items-center justify-center">
                  {/* 简洁的停止图标 */}
                  <div className="w-3 h-3 bg-white rounded-sm animate-spin" style={{animationDuration: '2s'}}></div>
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  {/* 发送图标 */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="group-hover:animate-[takeoff_1.5s_ease-out_infinite] transition-transform duration-200"
                  >
                    <path d="M22 2L11 13"/>
                    <path d="M22 2L15 22L11 13L2 9L22 2Z"/>
                  </svg>
                </div>
              )}

              {/* 简单的点击反馈效果 */}
              <div className="absolute inset-0 rounded-full bg-white opacity-0 scale-0 group-active:opacity-20 group-active:scale-110 transition-all duration-150"></div>
            </button>
          </div>
        </div>
        {/* 免责声明 */}
        <div className="text-center mt-2">
          <p className="text-xs text-gray-400">
            {t('chat.aiDisclaimer')}
          </p>
        </div>

        {/* 点击外部关闭模式菜单 */}
        {showModeMenu && (
          <div
            className="fixed inset-0 z-20"
            onClick={() => setShowModeMenu(false)}
          />
        )}
      </div>
    </div>
  )
}

export default ChatInput