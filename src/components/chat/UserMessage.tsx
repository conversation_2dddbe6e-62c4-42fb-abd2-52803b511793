import type React from 'react'
import { useSimpleTranslation } from '../../i18n/simple-hooks'
import { formatSize, getFileIcon } from '../../api/src/utils/file-type';

// 文件上传项类型定义
interface UploadFileItem {
  uid: string
  name: string
  status: 'uploading' | 'done' | 'error'
  size: number
  type: 'document' | 'image' | 'audio' | 'video'
  originFileObj?: { uid: string }
  percent: number
  transfer_method: 'local_file'
  upload_file_id?: string
  error?: string
  url?: string
}

interface UserMessageProps {
  content: string
  files?: UploadFileItem[]
}

const openAttachment = (url: string) => {
  window.open(url, '_blank')
}

/**
 * 用户消息组件 - 显示用户发送的消息
 * 采用右对齐布局，灰色背景样式
 */
const UserMessage: React.FC<UserMessageProps> = ({ content, files }) => {
  const { t } = useSimpleTranslation()
  return (
    <div className="mx-auto max-w-4xl flex justify-end">
      <div className="inline-block max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200">
        {/* 文字内容 */}
        {content && (
          <div className="text-gray-800 text-sm md:text-base leading-relaxed whitespace-pre-wrap">
            {content}
          </div>
        )}

        {/* 附件列表 */}
        {files && files.length > 0 && (
          <div className="space-y-2 mt-3">
            {files.map((file) => (
              <div
                key={file.uid}
                className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
              >
                {/* 文件图标 */}
                <div className="flex-shrink-0 text-lg">
                  {getFileIcon(file.type)}
                </div>

                {/* 文件信息 */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatSize(file.size)}
                    {file.status === 'uploading' && ` • 上传中 ${file.percent}%`}
                    {file.status === 'error' && file.error && ` • ${file.error}`}
                    {file.status === 'done' && ` • ${t('chat.uploaded')}`}
                  </p>
                </div>

                {/* 状态指示器 */}
                <div className="flex-shrink-0" >
                  {file.status === 'uploading' && (
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  )}
                  {file.status === 'done' && (
                    <div className="flex-shrink-0 ml-3 cursor-pointer" onClick={() => openAttachment(file.url!)}>
                      <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor" className="remixicon h-3.5 w-3.5 text-text-tertiary">
                        <path d="M3 19H21V21H3V19ZM13 13.1716L19.0711 7.1005L20.4853 8.51472L12 17L3.51472 8.51472L4.92893 7.1005L11 13.1716V2H13V13.1716Z"></path>
                      </svg>
                    </div>
                  )}
                  {file.status === 'error' && (
                    <div className="text-red-500">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default UserMessage