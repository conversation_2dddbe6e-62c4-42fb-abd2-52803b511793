import type React from 'react';
import { useState, useCallback, useRef, useEffect } from 'react';
import { CopyIcon, CheckIcon } from '../icons/Icons';
import { copyToClipboard, extractPlainText, isClipboardSupported } from '../../utils/textUtils';
import { useSimpleTranslation } from '../../i18n/simple-hooks';

export interface CopyButtonProps {
  /** 要复制的内容 */
  content: string;
  /** 自定义样式类 */
  className?: string;
  /** 按钮大小 */
  size?: 'sm' | 'md' | 'lg';
  /** 按钮位置 */
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'inline';
  /** 是否显示工具提示 */
  showTooltip?: boolean;
  /** 复制成功回调 */
  onCopySuccess?: () => void;
  /** 复制失败回调 */
  onCopyError?: (error: Error) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义图标大小 */
  iconSize?: number;
  /** 是否显示边框 */
  showBorder?: boolean;
}

type CopyState = 'idle' | 'copying' | 'success' | 'error';

const CopyButton: React.FC<CopyButtonProps> = ({
  content,
  className = '',
  size = 'md',
  position = 'top-right',
  showTooltip = true,
  onCopySuccess,
  onCopyError,
  disabled = false,
  iconSize,
  showBorder = true
}) => {
  const { t } = useSimpleTranslation();
  const [copyState, setCopyState] = useState<CopyState>('idle');
  const [showTooltipState, setShowTooltipState] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
    };
  }, []);

  // 获取按钮尺寸配置
  const getSizeConfig = () => {
    const configs = {
      sm: { 
        button: 'w-6 h-6', 
        icon: iconSize || 12,
        tooltip: 'text-xs'
      },
      md: { 
        button: 'w-8 h-8', 
        icon: iconSize || 14,
        tooltip: 'text-sm'
      },
      lg: { 
        button: 'w-10 h-10', 
        icon: iconSize || 16,
        tooltip: 'text-sm'
      }
    };
    return configs[size];
  };

  // 获取位置样式
  const getPositionStyles = () => {
    const positions = {
      'top-right': 'absolute top-2 right-2',
      'top-left': 'absolute top-2 left-2',
      'bottom-right': 'absolute bottom-2 right-2',
      'bottom-left': 'absolute bottom-2 left-2',
      'inline': 'relative'
    };
    return positions[position];
  };

  // 复制处理函数
  const handleCopy = useCallback(async () => {
    if (disabled || copyState === 'copying' || !content) {
      return;
    }

    // 检查是否支持复制功能
    if (!isClipboardSupported()) {
      setCopyState('error');
      const error = new Error(t('chat.copyFailed'));
      onCopyError?.(error);
      
      // 2秒后重置状态
      timeoutRef.current = setTimeout(() => {
        setCopyState('idle');
      }, 2000);
      return;
    }

    setCopyState('copying');

    try {
      // 提取纯文本
      const plainText = extractPlainText(content);
      
      // 复制到剪贴板
      const success = await copyToClipboard(plainText);
      
      if (success) {
        setCopyState('success');
        onCopySuccess?.();
        
        // 2秒后重置状态
        timeoutRef.current = setTimeout(() => {
          setCopyState('idle');
        }, 2000);
      } else {
        throw new Error('复制操作失败');
      }
    } catch (error) {
      setCopyState('error');
      const copyError = error instanceof Error ? error : new Error(t('chat.copyFailed'));
      onCopyError?.(copyError);
      
      // 2秒后重置状态
      timeoutRef.current = setTimeout(() => {
        setCopyState('idle');
      }, 2000);
    }
  }, [content, disabled, copyState, onCopySuccess, onCopyError, t]);

  // 鼠标悬停处理
  const handleMouseEnter = useCallback(() => {
    if (showTooltip && copyState === 'idle') {
      setShowTooltipState(true);
    }
  }, [showTooltip, copyState]);

  const handleMouseLeave = useCallback(() => {
    setShowTooltipState(false);
  }, []);

  // 键盘事件处理
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleCopy();
    }
  }, [handleCopy]);

  const sizeConfig = getSizeConfig();
  const positionStyles = getPositionStyles();

  // 获取当前状态的图标和样式
  const getCurrentIcon = () => {
    switch (copyState) {
      case 'success':
        return (
          <CheckIcon 
            width={sizeConfig.icon} 
            height={sizeConfig.icon} 
            className="text-green-600"
          />
        );
      case 'error':
        return (
          <CopyIcon 
            width={sizeConfig.icon} 
            height={sizeConfig.icon} 
            className="text-red-500"
          />
        );
      case 'copying':
        return (
          <CopyIcon 
            width={sizeConfig.icon} 
            height={sizeConfig.icon} 
            className="text-blue-500 animate-pulse"
          />
        );
      default:
        return (
          <CopyIcon 
            width={sizeConfig.icon} 
            height={sizeConfig.icon} 
            className="text-gray-500 group-hover:text-gray-700 transition-colors"
          />
        );
    }
  };

  // 获取工具提示文本
  const getTooltipText = () => {
    switch (copyState) {
      case 'success':
        return t('chat.copied');
      case 'error':
        return t('chat.copyFailed');
      case 'copying':
        return t('common.loading');
      default:
        return t('chat.copyMessage');
    }
  };

  return (
    <div className={`${positionStyles} z-10`}>
      <button
        onClick={handleCopy}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onKeyDown={handleKeyDown}
        disabled={disabled || copyState === 'copying'}
        className={`
          ${sizeConfig.button}
          flex items-center justify-center
          rounded-md
          ${showBorder
            ? 'bg-white/80 hover:bg-white border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md'
            : 'bg-gray-100/80 hover:bg-gray-200/80'
          }
          transition-all duration-200
          group
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
          disabled:opacity-50 disabled:cursor-not-allowed
          ${copyState === 'success' ? (showBorder ? 'bg-green-50 border-green-200' : 'bg-green-100/80') : ''}
          ${copyState === 'error' ? (showBorder ? 'bg-red-50 border-red-200' : 'bg-red-100/80') : ''}
          ${className}
        `}
        aria-label={getTooltipText()}
        title={getTooltipText()}
      >
        {getCurrentIcon()}
      </button>

      {/* 工具提示 */}
      {showTooltip && (showTooltipState || copyState !== 'idle') && (
        <div className={`
          absolute z-20 px-2 py-1 
          bg-gray-900 text-white 
          rounded text-xs
          whitespace-nowrap
          pointer-events-none
          transition-opacity duration-200
          ${position.includes('top') ? 'bottom-full mb-1' : 'top-full mt-1'}
          ${position.includes('right') ? 'right-0' : 'left-0'}
          ${sizeConfig.tooltip}
        `}>
          {getTooltipText()}
          {/* 箭头 */}
          <div className={`
            absolute w-0 h-0
            border-l-4 border-r-4 border-transparent
            ${position.includes('top') 
              ? 'top-full border-t-4 border-t-gray-900' 
              : 'bottom-full border-b-4 border-b-gray-900'
            }
            ${position.includes('right') ? 'right-2' : 'left-2'}
          `} />
        </div>
      )}
    </div>
  );
};

export default CopyButton;
