import type React from 'react'
import { useSimpleTranslation } from '../../i18n/simple-hooks'

export type Mode = '简单模式' | '精细模式'

interface ModeSelectorProps {
  value: Mode
  onChange: (mode: Mode) => void
  className?: string
  disabled?: boolean
}

/**
 * 模式选择组件 - 简洁的下拉选择框样式
 * 支持移动端触摸交互和响应式设计
 */
const ModeSelector: React.FC<ModeSelectorProps> = ({
  value,
  onChange,
  className = '',
  disabled = false
}) => {
  const { t } = useSimpleTranslation()

  const modes: { value: Mode; labelKey: string }[] = [
    {
      value: '简单模式',
      labelKey: 'mode.simple'
    },
    {
      value: '精细模式',
      labelKey: 'mode.detailed'
    }
  ]

  return (
    <div className={`mode-selector ${className}`}>
      <select
        value={value}
        onChange={(e) => !disabled && onChange(e.target.value as Mode)}
        disabled={disabled}
        className={`
          w-full px-3 py-2 text-sm border border-gray-200 rounded-lg
          bg-white text-gray-700
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          hover:border-gray-300
          transition-all duration-200
          ${disabled
            ? 'opacity-50 cursor-not-allowed bg-gray-50'
            : 'cursor-pointer'
          }

          /* 移动端优化 */
          min-h-[44px] /* 确保触摸目标足够大 */
          touch-manipulation

          /* 响应式字体大小 */
          text-sm md:text-base
        `}
        aria-label={t('mode.selectMode')}
      >
        {modes.map((mode) => (
          <option key={mode.value} value={mode.value}>
            {t(mode.labelKey)}
          </option>
        ))}
      </select>
    </div>
  )
}

export default ModeSelector
