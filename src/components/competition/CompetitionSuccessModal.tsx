import type React from 'react';
import { Mo<PERSON>, Button } from 'antd';
import { useSimpleTranslation } from '../../i18n/simple-hooks';

interface CompetitionSuccessModalProps {
  open: boolean;
  onClose: () => void;
  onFillSurvey?: () => void;
}

const CompetitionSuccessModal: React.FC<CompetitionSuccessModalProps> = ({
  open,
  onClose,
  onFillSurvey
}) => {
  const { t } = useSimpleTranslation();

  const handleFillSurvey = () => {
    onFillSurvey?.();
    onClose();
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={480}
      closable={false}
      maskClosable={false}
    >
      <div className="relative text-center py-6">
        {/* 右上角关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-2 right-2 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors duration-200 group"
          aria-label="关闭"
        >
          <svg
            className="w-5 h-5 text-gray-400 group-hover:text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        {/* 成功图标 */}
        <div className="mb-6">
          <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto shadow-lg">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </div>

        {/* 标题 */}
        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          {t('competition.submissionSuccessTitle')}
        </h2>

        {/* 描述文字 */}
        <div className="text-gray-600 mb-8 space-y-2">
          <p>{t('competition.submissionSuccessMessage')}</p>
        </div>

        {/* 调研问卷链接 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span className="text-blue-800 font-medium">{t('competition.surveyTitle')}</span>
          </div>
          <p className="text-blue-700 text-sm mb-3">
            {t('competition.surveyDescription')}
          </p>
          <Button
            type="primary"
            onClick={handleFillSurvey}
            className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700"
            size="large"
          >
            {t('competition.fillSurvey')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CompetitionSuccessModal;
