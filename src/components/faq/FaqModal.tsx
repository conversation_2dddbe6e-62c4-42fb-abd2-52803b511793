import type React from 'react';
import { useState, useEffect, useMemo } from 'react';
import { Modal, Collapse } from 'antd';
import { useSimpleTranslation } from '../../i18n/simple-hooks';
import { XAiApi } from '../../api/src/xai-api';
import { getApiConfig } from '../../utils/apiConfig';
import './FaqModal.css';

interface FaqModalProps {
  open: boolean;
  onClose: () => void;
}

interface FaqItem {
  Q: string;
  A: string;
}

const FaqModal: React.FC<FaqModalProps> = ({ open, onClose }) => {
  const { t, currentLanguage } = useSimpleTranslation();
  const [faqData, setFaqData] = useState<FaqItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 创建 XAiApi 实例
  const xAiApi = useMemo(() => {
    const apiConfig = getApiConfig();
    return new XAiApi({
      user: apiConfig.user,
      apiBase: apiConfig.apiBase,
      medxyToken: apiConfig.medxyToken
    });
  }, []);

  // 获取FAQ数据
  useEffect(() => {
    const fetchFaqData = async () => {
      if (!open) return; // 只有在模态框打开时才获取数据

      setLoading(true);
      try {
        const response = await xAiApi.getConfigByKeyFromCache({
          configKey: 'project_faq'
        });

        if (response && typeof response === 'object') {
          const faq = response[currentLanguage] ? response[currentLanguage] : (currentLanguage==='en'?response['en']:response['zh']);
          setFaqData(faq);
        } else {
          setFaqData([]);
        }
      } catch (error) {
        console.error('获取FAQ数据失败:', error);
        setFaqData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFaqData();
  }, [open, xAiApi]);

  // 将FAQ数据转换为Collapse组件需要的格式
  const collapseItems = faqData.map((item, index) => ({
    key: index.toString(),
    label: (
      <div className="font-medium text-gray-800 text-base">
        {item.Q}
      </div>
    ),
    children: (
      <div
        className="text-gray-600 leading-relaxed text-sm"
        dangerouslySetInnerHTML={{ __html: item.A }}
      />
    ),
  }));

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={600}
      zIndex={5000}
      title="FAQ"
      className="faq-modal"
    >
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">{t('common.loading')}</div>
        </div>
      ) : faqData.length > 0 ? (
        <div className="max-h-96 overflow-y-auto faq-scroll-container">
          <Collapse
            items={collapseItems}
            ghost
            size="large"
            className="faq-collapse"
            expandIconPosition="end"
          />
        </div>
      ) : (
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">{t('footer.noFaq')}</div>
        </div>
      )}
    </Modal>
  );
};

export default FaqModal;
