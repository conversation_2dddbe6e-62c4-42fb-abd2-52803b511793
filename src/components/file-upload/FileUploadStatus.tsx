import type React from 'react'
import { useState } from 'react'
import { useSimpleTranslation } from '../../i18n/simple-hooks'

export interface FileUploadStatusItem {
  uid: string
  name: string
  status: 'uploading' | 'done' | 'error'
  size: number
  type: 'document' | 'image' | 'audio' | 'video'
  percent?: number
  error?: string
  upload_file_id?: string
}

interface FileUploadStatusProps {
  files: FileUploadStatusItem[]
  onRemove?: (uid: string) => void
  showProgress?: boolean
  compact?: boolean
  layout?: 'vertical' | 'horizontal'
  maxFileNameLength?: number
  className?: string
}

/**
 * 文件上传状态组件 - 显示文件上传进度和状态
 * 支持局部加载动画，适用于首页和对话详情页
 */
const FileUploadStatus: React.FC<FileUploadStatusProps> = ({
  files,
  onRemove,
  showProgress = true,
  compact = false,
  layout = 'vertical',
  maxFileNameLength = 18,
  className = ''
}) => {
  const { t } = useSimpleTranslation()
  const [hoveredFile, setHoveredFile] = useState<string | null>(null)

  if (files.length === 0) return null

  // 文件名截断函数
  const truncateFileName = (fileName: string, maxLength: number = maxFileNameLength): string => {
    if (fileName.length <= maxLength) return fileName

    const extension = fileName.split('.').pop() || ''
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName
    const maxNameLength = maxLength - extension.length - 4 // 4 for "..." and "."

    if (maxNameLength <= 0) return `...${extension}`

    return `${nameWithoutExt.substring(0, maxNameLength)}...${extension ? '.' + extension : ''}`
  }

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return '🖼️'
      case 'document': return '📄'
      case 'audio': return '🎵'
      case 'video': return '🎬'
      default: return '📎'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return (
          <div className="w-4 h-4 relative">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )
      case 'done':
        return (
          <div className="text-green-500">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        )
      case 'error':
        return (
          <div className="text-red-500">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        )
      default:
        return null
    }
  }

  // 水平布局渲染
  if (layout === 'horizontal') {
    return (
      <div className={`flex flex-wrap gap-2 ${className}`}>
        {files.map((file) => (
          <div
            key={file.uid}
            className="relative"
            onMouseEnter={() => setHoveredFile(file.uid)}
            onMouseLeave={() => setHoveredFile(null)}
          >
            {/* Tooltip */}
            {hoveredFile === file.uid && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap z-10">
                {file.name}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
              </div>
            )}

            {/* 文件标签 */}
            <div className={`inline-flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-full border text-sm transition-all duration-200 hover:shadow-md ${
              compact ? 'px-2 py-1 text-xs' : 'px-3 py-2 text-sm'
            }`}>
              {/* 文件图标 */}
              <div className="flex-shrink-0 text-sm">
                {getFileIcon(file.type)}
              </div>

              {/* 文件名 */}
              <span className="font-medium text-gray-900 max-w-[120px] truncate">
                {truncateFileName(file.name)}
              </span>

              {/* 状态指示 */}
              <div className="flex items-center gap-1">
                {file.status === 'uploading' && showProgress && (
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 relative">
                      <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                    <span className="text-xs text-blue-600 font-medium">{file.percent || 0}%</span>
                  </div>
                )}

                {file.status === 'done' && (
                  <div className="text-green-500">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                )}

                {file.status === 'error' && (
                  <div className="text-red-500">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                )}

                {/* 删除按钮 */}
                {onRemove && (
                  <button
                    onClick={() => onRemove(file.uid)}
                    className="ml-1 p-0.5 text-gray-400 hover:text-red-500 transition-colors touch-manipulation"
                    disabled={file.status === 'uploading'}
                    title={
                      file.status === 'uploading'
                        ? t('fileUpload.uploadingCannotDelete')
                        : t('fileUpload.removeFile')
                    }
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            {/* 水平布局的进度条 */}
            {showProgress && file.status === 'uploading' && (
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-600 transition-all duration-300 rounded-full"
                  style={{ width: `${file.percent || 0}%` }}
                ></div>
              </div>
            )}
          </div>
        ))}
      </div>
    )
  }

  // 垂直布局渲染（原有布局）
  return (
    <div className={`space-y-2 ${className}`}>
      {files.map((file) => (
        <div
          key={file.uid}
          className={`flex items-center justify-between p-3 bg-gray-50 rounded-lg border text-sm transition-all duration-200 ${
            compact ? 'p-2' : 'p-3'
          }`}
        >
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            {/* 文件图标 */}
            <div className="flex-shrink-0 text-lg">
              {getFileIcon(file.type)}
            </div>

            {/* 文件信息 */}
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">{file.name}</p>
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                <span>{(file.size / (1024 * 1024)).toFixed(2)} MB</span>
                {file.status === 'uploading' && (
                  <>
                    <span>•</span>
                    <span className="text-blue-600 font-medium">
                      {t('fileUpload.uploading')} {file.percent || 0}%
                    </span>
                  </>
                )}
                {file.status === 'error' && file.error && (
                  <>
                    <span>•</span>
                    <span className="text-red-600 font-medium">{file.error}</span>
                  </>
                )}
                {file.status === 'done' && (
                  <>
                    <span>•</span>
                    <span className="text-green-600 font-medium">{t('fileUpload.uploadComplete')}</span>
                  </>
                )}
              </div>

              {/* 进度条 */}
              {showProgress && file.status === 'uploading' && (
                <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
                  <div
                    className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${file.percent || 0}%` }}
                  ></div>
                </div>
              )}
            </div>
          </div>

          {/* 状态和操作按钮 */}
          <div className="flex items-center space-x-2 ml-3">
            {/* 状态图标 */}
            {getStatusIcon(file.status)}

            {/* 删除按钮 */}
            {onRemove && (
              <button
                onClick={() => onRemove(file.uid)}
                className="p-1 text-gray-400 hover:text-red-500 transition-colors touch-manipulation"
                disabled={file.status === 'uploading'}
                title={
                  file.status === 'uploading'
                    ? t('fileUpload.uploadingCannotDelete')
                    : t('fileUpload.removeFile')
                }
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}

export default FileUploadStatus
