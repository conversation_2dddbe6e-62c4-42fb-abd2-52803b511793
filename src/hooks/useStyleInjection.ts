/**
 * React Hook for managing dynamic CSS style injection
 * 用于管理动态CSS样式注入的React Hook
 */

import { useEffect, useRef, useCallback } from 'react';
import { 
  processAIResponseStyles, 
  removeInjectedCSS, 
  type StyleProcessResult 
} from '../utils/styleUtils';

interface UseStyleInjectionOptions {
  /** 作用域前缀，用于限制样式作用范围 */
  scopePrefix?: string;
  /** 是否在组件卸载时自动清理样式 */
  autoCleanup?: boolean;
}

interface UseStyleInjectionReturn {
  /** 处理包含样式的HTML内容 */
  processContent: (htmlContent: string) => string;
  /** 手动清理所有注入的样式 */
  cleanupStyles: () => void;
  /** 当前注入的样式ID列表 */
  injectedStyleIds: string[];
}

/**
 * 用于管理AI回复内容中CSS样式注入的Hook
 * @param options 配置选项
 * @returns Hook返回值
 */
export const useStyleInjection = (
  options: UseStyleInjectionOptions = {}
): UseStyleInjectionReturn => {
  const {
    scopePrefix = '.ai-response-container',
    autoCleanup = true
  } = options;

  // 存储当前注入的样式ID
  const injectedStyleIdsRef = useRef<string[]>([]);
  
  // 存储最后处理的内容，避免重复处理
  const lastProcessedContentRef = useRef<string>('');
  const lastProcessResultRef = useRef<StyleProcessResult | null>(null);

  /**
   * 清理所有注入的样式
   */
  const cleanupStyles = useCallback(() => {
    injectedStyleIdsRef.current.forEach(styleId => {
      removeInjectedCSS(styleId);
    });
    injectedStyleIdsRef.current = [];
    lastProcessedContentRef.current = '';
    lastProcessResultRef.current = null;
  }, []);

  /**
   * 处理包含样式的HTML内容
   * @param htmlContent HTML内容字符串
   * @returns 清理后的HTML内容
   */
  const processContent = useCallback((htmlContent: string): string => {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return htmlContent;
    }

    // 如果内容没有变化，直接返回上次的结果
    if (htmlContent === lastProcessedContentRef.current && lastProcessResultRef.current) {
      return lastProcessResultRef.current.cleanedHTML;
    }

    // 清理之前的样式（如果有的话）
    if (injectedStyleIdsRef.current.length > 0) {
      cleanupStyles();
    }

    // 处理新的内容
    const result = processAIResponseStyles(htmlContent, scopePrefix);
    
    // 更新引用
    injectedStyleIdsRef.current = result.styleIds;
    lastProcessedContentRef.current = htmlContent;
    lastProcessResultRef.current = result;

    return result.cleanedHTML;
  }, [scopePrefix, cleanupStyles]);

  // 组件卸载时自动清理样式
  useEffect(() => {
    return () => {
      if (autoCleanup) {
        cleanupStyles();
      }
    };
  }, [autoCleanup, cleanupStyles]);

  return {
    processContent,
    cleanupStyles,
    injectedStyleIds: injectedStyleIdsRef.current
  };
};

/**
 * 用于单次样式处理的简化Hook
 * 适用于不需要管理样式生命周期的场景
 * @param htmlContent HTML内容
 * @param scopePrefix 作用域前缀
 * @returns 处理后的HTML内容
 */
export const useProcessedContent = (
  htmlContent: string,
  scopePrefix = '.ai-response-container'
): string => {
  const { processContent } = useStyleInjection({ scopePrefix });
  
  return processContent(htmlContent);
};

export default useStyleInjection;
