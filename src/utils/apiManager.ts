import { XAiApi } from '../api/src/xai-api'
import { DifyApi } from '../api/src/dify-api'
import { getEnvConfig } from './envConfig'
import Cookies from 'js-cookie'

/**
 * API实例管理器
 * 负责创建和管理API实例，确保token始终是最新的
 */
class ApiManager {
  private xAiApiInstance: XAiApi | null = null
  private difyApiInstance: DifyApi | null = null
  private currentUser = 'nologin'

  /**
   * 获取XAiApi实例
   * 如果实例不存在或用户发生变化，则重新创建
   */
  getXAiApi(user?: string): XAiApi {
    const envConfig = getEnvConfig()
    const apiBase = envConfig.apiBase
    const medxyToken = Cookies.get('medxyToken') || ''
    const currentUser = user || this.currentUser

    // 如果实例不存在或用户发生变化，重新创建实例
    if (!this.xAiApiInstance || this.currentUser !== currentUser) {
      this.currentUser = currentUser
      this.xAiApiInstance = new XAiApi({
        user: currentUser,
        apiBase: apiBase,
        medxyToken: medxyToken
      })
    }

    return this.xAiApiInstance
  }

  /**
   * 获取DifyApi实例
   * 如果实例不存在或用户发生变化，则重新创建
   */
  getDifyApi(user?: string, appId?: string): DifyApi {
    const envConfig = getEnvConfig()
    const apiBase = envConfig.apiBase
    const medxyToken = Cookies.get('medxyToken') || ''
    const currentUser = user || this.currentUser

    // 如果实例不存在或用户发生变化，重新创建实例
    if (!this.difyApiInstance || this.currentUser !== currentUser) {
      this.currentUser = currentUser
      this.difyApiInstance = new DifyApi({
        user: currentUser,
        apiBase: apiBase,
        medxyToken: medxyToken,
        appId: appId
      })
    }

    return this.difyApiInstance
  }

  /**
   * 更新API实例的token
   * 在登录状态变化时调用
   */
  updateToken(newToken: string, user?: string) {
    if (user) {
      this.currentUser = user
    }

    // 更新XAiApi实例的token
    if (this.xAiApiInstance) {
      this.xAiApiInstance.updateOptions({
        user: this.currentUser,
        apiBase: getEnvConfig().apiBase,
        medxyToken: newToken
      })
    }

    // 更新DifyApi实例的token
    if (this.difyApiInstance) {
      this.difyApiInstance.updateOptions({
        user: this.currentUser,
        apiBase: getEnvConfig().apiBase,
        medxyToken: newToken,
        appId: this.difyApiInstance.options.appId
      })
    }
  }

  /**
   * 清除所有API实例
   * 在退出登录时调用
   */
  clearInstances() {
    this.xAiApiInstance = null
    this.difyApiInstance = null
    this.currentUser = 'nologin'
  }

  /**
   * 监听登录状态变化
   * 自动更新API实例的token
   */
  initLoginStatusListener() {
    // 监听用户信息更新事件
    window.addEventListener('userInfoUpdated', ((event: CustomEvent) => {
      const { userInfo } = event.detail
      if (userInfo) {
        const newToken = Cookies.get('medxyToken') || ''
        this.updateToken(newToken, userInfo.userName)
        console.log('🔄 API实例token已更新:', newToken)
      }
    }) as EventListener)

    // 监听登录状态变化事件
    window.addEventListener('loginStatusChanged', ((event: CustomEvent) => {
      const { type } = event.detail
      if (type === 'logout') {
        this.clearInstances()
        console.log('🧹 API实例已清除')
      }
    }) as EventListener)
  }
}

// 创建全局API管理器实例
export const apiManager = new ApiManager()

// 初始化登录状态监听器
apiManager.initLoginStatusListener()

/**
 * 获取XAiApi实例的便捷方法
 */
export const getXAiApi = (user?: string) => {
  return apiManager.getXAiApi(user)
}

/**
 * 获取DifyApi实例的便捷方法
 */
export const getDifyApi = (user?: string, appId?: string) => {
  return apiManager.getDifyApi(user, appId)
}
