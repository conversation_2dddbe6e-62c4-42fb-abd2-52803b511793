// 知识库API服务类
// 用于替换本地存储实现，使用真实的API调用

import type { XAiApi, knowledgeItem } from '../api/src/xai-api';
import type { KnowledgebaseItem, ItemType } from './knowledgebaseStorage';

export class KnowledgebaseApiService {
  private static instance: KnowledgebaseApiService;
  private xAiApi: XAiApi;

  private constructor(xAiApi: XAiApi) {
    this.xAiApi = xAiApi;
  }

  public static getInstance(xAiApi: XAiApi): KnowledgebaseApiService {
    if (!KnowledgebaseApiService.instance) {
      KnowledgebaseApiService.instance = new KnowledgebaseApiService(xAiApi);
    }
    return KnowledgebaseApiService.instance;
  }

  // 将API返回的knowledgeItem转换为本地KnowledgebaseItem格式
  private convertApiItemToLocal(apiItem: knowledgeItem): KnowledgebaseItem {
    // fileName应该就是文件名或文件夹名，不包含路径
    const actualFileName = apiItem.fileName;
    
    console.log('convertApiItemToLocal调试:', {
      原始数据: apiItem,
      actualFileName,
      type: apiItem.type
    });
    
    const convertedItem: KnowledgebaseItem = {
      id: apiItem.id.toString(),
      name: actualFileName,
      type: (apiItem.type === 1 ? 'folder' : 'file') as ItemType, // type=1为目录，type=2为文件
      path: [], // 由于API直接返回当前目录下的项目，路径为空
      size: apiItem.fileSize || 0,
      uploadTime: new Date(apiItem.createdAt).getTime(),
      mimeType: this.getMimeTypeFromFileName(actualFileName),
      data: apiItem.cdnSignedUrl // 使用CDN URL而不是base64数据
    };
    
    console.log('转换结果:', convertedItem);
    return convertedItem;
  }

  // 从文件名解析路径（这里需要根据实际API结构调整）
  private parsePathFromFileName(fileName: string): string[] {
    // 如果fileName包含路径信息，解析出来
    // 这里假设fileName可能包含路径，如 "folder1/folder2/file.txt"
    const parts = fileName.split('/');
    return parts.length > 1 ? parts.slice(0, -1) : [];
  }

  // 根据文件名获取MIME类型
  private getMimeTypeFromFileName(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif'
    };
    return mimeTypes[extension || ''] || 'application/octet-stream';
  }

  // 构建路径字符串
  private buildPathString(path: string[]): string {
    return path.join('/');
  }

  // 根据路径获取当前目录的ID
  public async getCurrentDirIdByPath(path: string[]): Promise<number> {
    // 如果是根目录，返回0
    if (path.length === 0) {
      return 0;
    }
    
    console.log('getCurrentDirIdByPath - 查找路径对应的目录ID:', path);
    
    // 从根目录开始，逐级查找每个路径段对应的目录ID
    let currentPid = 0; // 从根目录开始
    
    for (const folderName of path) {
      try {
        // 获取当前目录下的所有项目
        const apiItems = await this.xAiApi.getKnowledgeByDir({ pid: currentPid, fileName: '' });
        
        // 查找匹配的文件夹
        const targetFolder = apiItems.find(item => 
          item.type === 1 && // type=1为目录
          item.fileName === folderName
        );
        
        if (!targetFolder) {
          console.error(`找不到文件夹: ${folderName} 在路径:`, path);
          return 0; // 如果找不到，返回根目录ID
        }
        
        currentPid = targetFolder.id; // 更新当前目录ID
        console.log(`找到文件夹 ${folderName}，ID: ${currentPid}`);
      } catch (error) {
        console.error('查找目录ID时出错:', error);
        return 0; // 出错时返回根目录ID
      }
    }
    
    return currentPid;
  }

  // 获取指定路径下的项目
  public async getItemsInPath(userId: string, path: string[]): Promise<KnowledgebaseItem[]> {
    try {
      // 获取当前目录的ID作为pid
      const pid = await this.getCurrentDirIdByPath(path);
      
      console.log('getItemsInPath调试信息:', { path, pid });
      
      // 调用API获取当前目录下的所有项目，不传fileName参数
      const apiItems = await this.xAiApi.getKnowledgeByDir({ pid, fileName: '' });
      
      console.log('API返回的原始数据:', apiItems);
      
      // 转换API数据为本地格式
      const convertedItems = apiItems.map(item => this.convertApiItemToLocal(item));
      console.log('转换后的数据:', convertedItems);
      
      // 由于API应该直接返回当前目录下的项目，所以不需要复杂的过滤逻辑
      // 只需要简单排序即可
      return convertedItems.sort((a, b) => {
        // 文件夹排在前面，同类型按id倒序排序
        if (a.type !== b.type) {
          return a.type === 'folder' ? -1 : 1;
        }
        return Number.parseInt(b.id) - Number.parseInt(a.id);
      });
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      throw new Error('获取知识库列表失败');
    }
  }

  // 根据父目录ID获取项目
  public async getItemsByParentId(userId: string, pid: number): Promise<KnowledgebaseItem[]> {
    try {
      console.log('getItemsByParentId调试信息:', { pid });
      
      // 调用API获取当前目录下的所有项目，不传fileName参数
      const apiItems = await this.xAiApi.getKnowledgeByDir({ pid, fileName: '' });
      
      console.log('API返回的原始数据:', apiItems);
      
      // 转换API数据为本地格式
      const convertedItems = apiItems.map(item => this.convertApiItemToLocal(item));
      console.log('转换后的数据:', convertedItems);
      
      // 由于API应该直接返回当前目录下的项目，所以不需要复杂的过滤逻辑
      // 只需要简单排序即可
      return convertedItems.sort((a, b) => {
        // 文件夹排在前面，然后按名称排序
        if (a.type !== b.type) {
          return a.type === 'folder' ? -1 : 1;
        }
        return Number.parseInt(b.id) - Number.parseInt(a.id);
      });
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      throw new Error('获取知识库列表失败');
    }
  }

  // 添加文件
  public async addFile(userId: string, pid: number, file: File): Promise<void> {
    try {
      const type = 2; // 文件类型
      const fileName = file.name; // 只使用文件名，不包含路径
      
      console.log('addFile调试信息:', { pid, fileName, type });
      
      await this.xAiApi.createKnowledge(type, pid, fileName, file);
    } catch (error) {
      console.error('文件上传失败:', error);
      
      // 检查是否是配额超限错误
      const errorMessage = (error as Error).message || '';
      if (errorMessage.includes('quota exceeded') || errorMessage.includes('Resource::kQuotaBytes quota exceeded')) {
        throw new Error('存储空间不足，无法上传文件。请删除一些文件后重试，或联系管理员扩容。');
      }
      
      throw error;
    }
  }

  // 创建文件夹
  public async createFolder(userId: string, pid: number, folderName: string): Promise<void> {
    try {
      const type = 1; // 文件夹类型
      const fileName = folderName; // 只使用文件夹名，不包含路径
      
      console.log('createFolder调试信息:', { pid, fileName, type });
      
      // 对于文件夹，可能不需要传递文件，根据API设计调整
      await this.xAiApi.createKnowledge(type, pid, fileName);
    } catch (error) {
      console.error('创建文件夹失败:', error);
      throw error;
    }
  }

  // 删除项目
  public async deleteItem(userId: string, itemId: string): Promise<void> {
    try {
      await this.xAiApi.deleteKnowledge({ id: Number.parseInt(itemId) });
    } catch (error) {
      console.error('删除失败:', error);
      throw new Error('删除失败');
    }
  }

  // 重命名项目
  public async renameItem(userId: string, itemId: string, newName: string): Promise<void> {
    try {
      await this.xAiApi.updateFileName({ id: Number.parseInt(itemId), fileName: newName });
    } catch (error) {
      console.error('重命名失败:', error);
      throw new Error('重命名失败');
    }
  }

  // 下载文件
  public async downloadFile(userId: string, item: KnowledgebaseItem): Promise<void> {
    try {
      if (item.data) {
        // 直接使用当前文件的cdnSignedUrl
        const link = document.createElement('a');
        link.href = item.data;
        link.download = item.name;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        throw new Error('文件下载链接不可用');
      }
    } catch (error) {
      console.error('下载失败:', error);
      throw new Error('下载失败');
    }
  }

  // 获取用户统计信息
  public async getUserStats(userId: string): Promise<{ itemCount: number; totalSize: number; folderCount: number; fileCount: number }> {
    try {
      // 获取根目录下的所有项目来计算统计信息
      const items = await this.getItemsInPath(userId, []);
      const fileCount = items.filter(item => item.type === 'file').length;
      const folderCount = items.filter(item => item.type === 'folder').length;
      const totalSize = items.reduce((total, item) => total + (item.type === 'file' ? item.size : 0), 0);
      
      return {
        itemCount: items.length,
        totalSize,
        fileCount,
        folderCount
      };
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return {
        itemCount: 0,
        totalSize: 0,
        fileCount: 0,
        folderCount: 0
      };
    }
  }

  // 清空用户数据（用于测试）
  public async clearUserData(userId: string): Promise<void> {
    // API版本可能不需要此功能，或者需要调用特定的清空接口
    console.warn('API版本暂不支持清空用户数据功能');
  }
}